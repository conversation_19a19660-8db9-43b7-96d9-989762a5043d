import React, { useRef, useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';

// 页面顺序配置
const PAGE_ORDER = ['/', '/projects', '/skills', '/about', '/contact'];
const PAGE_NAMES = ['首页', '项目', '技能', '关于', '联系'];

// 样式组件
const ScrollContainer = styled.div`
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
`;

const ContentWrapper = styled(motion.div)`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  will-change: transform;
`;

const PageIndicator = styled.div`
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 15px;
  z-index: 100;
`;

const DotContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  
  &:hover .dot-label {
    opacity: 1;
    transform: translateX(0);
  }
`;

const Dot = styled.div`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: ${({ active, theme }) => 
    active ? theme.accent : theme.textSecondary};
  opacity: ${({ active }) => (active ? 1 : 0.5)};
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    opacity: 0.8;
    transform: scale(1.2);
  }
`;

const DotLabel = styled.span`
  position: absolute;
  right: 25px;
  color: ${({ theme }) => theme.text};
  font-size: 14px;
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.3s ease;
  white-space: nowrap;
  pointer-events: none;
  
  &.dot-label {
    opacity: ${({ active }) => (active ? 0.8 : 0)};
  }
`;

const ProgressBar = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  height: 3px;
  background-color: ${({ theme }) => theme.accent};
  z-index: 100;
`;

// 页面切换动画变体
const pageVariants = {
  enter: (direction) => ({
    y: direction > 0 ? "100vh" : "-100vh",
    opacity: 0
  }),
  center: {
    y: 0,
    opacity: 1
  },
  exit: (direction) => ({
    y: direction < 0 ? "100vh" : "-100vh",
    opacity: 0
  })
};

// 页面切换动画过渡
const pageTransition = {
  type: "tween",
  ease: "anticipate",
  duration: 0.8
};

const ScrollablePage = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const containerRef = useRef(null);
  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const [touchStart, setTouchStart] = useState(0);
  const [progress, setProgress] = useState(0);
  const [direction, setDirection] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // 初始化当前页面索引
  useEffect(() => {
    const index = PAGE_ORDER.indexOf(location.pathname);
    if (index !== -1) {
      setCurrentPageIndex(index);
      setProgress((index / (PAGE_ORDER.length - 1)) * 100);
    }
  }, [location.pathname]);

  // 处理滚轮事件
  const handleWheel = (e) => {
    e.preventDefault(); // 阻止默认滚动行为
    
    if (isScrolling || isAnimating) return;
    
    setIsScrolling(true);
    
    // 确定滚动方向
    const newDirection = e.deltaY > 0 ? 1 : -1;
    setDirection(newDirection);
    
    if (newDirection > 0 && currentPageIndex < PAGE_ORDER.length - 1) {
      // 向下滚动
      navigateToPage(currentPageIndex + 1, newDirection);
    } else if (newDirection < 0 && currentPageIndex > 0) {
      // 向上滚动
      navigateToPage(currentPageIndex - 1, newDirection);
    } else {
      // 如果没有导航，也要重置滚动状态
      setIsScrolling(false);
    }
  };

  // 处理触摸事件（移动端）
  const handleTouchStart = (e) => {
    setTouchStart(e.touches[0].clientY);
  };

  const handleTouchEnd = (e) => {
    if (isScrolling || isAnimating) return;
    
    const touchEnd = e.changedTouches[0].clientY;
    const diff = touchStart - touchEnd;
    
    if (Math.abs(diff) < 50) return; // 小于阈值不触发
    
    setIsScrolling(true);
    
    // 确定滑动方向
    const newDirection = diff > 0 ? 1 : -1;
    setDirection(newDirection);
    
    if (newDirection > 0 && currentPageIndex < PAGE_ORDER.length - 1) {
      // 向上滑动（下一页）
      navigateToPage(currentPageIndex + 1, newDirection);
    } else if (newDirection < 0 && currentPageIndex > 0) {
      // 向下滑动（上一页）
      navigateToPage(currentPageIndex - 1, newDirection);
    } else {
      setIsScrolling(false);
    }
  };

  // 点击导航点切换页面
  const handleDotClick = (index) => {
    if (isScrolling || isAnimating || index === currentPageIndex) return;
    
    const newDirection = index > currentPageIndex ? 1 : -1;
    setDirection(newDirection);
    navigateToPage(index, newDirection);
  };

  // 导航到指定页面
  const navigateToPage = (index, dir) => {
    setIsScrolling(true);
    setIsAnimating(true);
    const targetPage = PAGE_ORDER[index];
    navigate(targetPage);
    setCurrentPageIndex(index);
    setProgress((index / (PAGE_ORDER.length - 1)) * 100);
    
    // 防抖，避免连续滚动，动画完成后重置状态
    setTimeout(() => {
      setIsScrolling(false);
      setIsAnimating(false);
    }, 800); // 与动画持续时间匹配
  };

  // 处理键盘事件
  const handleKeyDown = (e) => {
    if (isScrolling || isAnimating) return;
    
    if (e.key === 'ArrowDown' && currentPageIndex < PAGE_ORDER.length - 1) {
      e.preventDefault();
      setDirection(1);
      navigateToPage(currentPageIndex + 1, 1);
    } else if (e.key === 'ArrowUp' && currentPageIndex > 0) {
      e.preventDefault();
      setDirection(-1);
      navigateToPage(currentPageIndex - 1, -1);
    }
  };

  // 添加和移除事件监听
  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('wheel', handleWheel, { passive: false });
      container.addEventListener('touchstart', handleTouchStart, { passive: true });
      container.addEventListener('touchend', handleTouchEnd, { passive: true });
      window.addEventListener('keydown', handleKeyDown);
      
      return () => {
        container.removeEventListener('wheel', handleWheel);
        container.removeEventListener('touchstart', handleTouchStart);
        container.removeEventListener('touchend', handleTouchEnd);
        window.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [currentPageIndex, isScrolling, isAnimating]);

  return (
    <ScrollContainer ref={containerRef}>
      <ProgressBar style={{ width: `${progress}%` }} />
      
      <AnimatePresence initial={false} custom={direction} mode="wait">
        <ContentWrapper
          key={location.pathname}
          custom={direction}
          variants={pageVariants}
          initial="enter"
          animate="center"
          exit="exit"
          transition={pageTransition}
        >
          {children}
        </ContentWrapper>
      </AnimatePresence>
      
      <PageIndicator>
        {PAGE_ORDER.map((path, index) => (
          <DotContainer key={path}>
            <DotLabel className="dot-label" active={index === currentPageIndex}>
              {PAGE_NAMES[index]}
            </DotLabel>
            <Dot 
              active={index === currentPageIndex}
              onClick={() => handleDotClick(index)}
              data-path={path}
            />
          </DotContainer>
        ))}
      </PageIndicator>
    </ScrollContainer>
  );
};

export default ScrollablePage; 