import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import PageSection from '../components/PageSection';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  color: ${({ theme }) => theme.text};
  margin-bottom: 1rem;
  background: linear-gradient(
    to right,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
`;

const Description = styled.p`
  font-size: 1.1rem;
  color: ${({ theme }) => theme.textSecondary};
  max-width: 700px;
  margin: 0 auto;
`;

const Content = styled.div`
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const ProfileSection = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const Avatar = styled.img`
  width: 250px;
  height: 250px;
  border-radius: 50%;
  object-fit: cover;
  border: 5px solid ${({ theme }) => theme.borderLight};
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
`;

const Name = styled.h2`
  font-size: 1.8rem;
  color: ${({ theme }) => theme.text};
  margin-bottom: 0.5rem;
`;

const JobTitle = styled.p`
  font-size: 1.2rem;
  color: ${({ theme }) => theme.accent};
  margin-bottom: 1.5rem;
`;

const ContactInfo = styled.div`
  width: 100%;
`;

const ContactItem = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
`;

const Icon = styled.span`
  margin-right: 1rem;
  color: ${({ theme }) => theme.accent};
  font-size: 1.2rem;
`;

const InfoSection = styled.div``;

const SectionTitle = styled.h3`
  font-size: 1.5rem;
  color: ${({ theme }) => theme.text};
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid ${({ theme }) => theme.borderLight};
`;

const InfoBlock = styled.div`
  margin-bottom: 2rem;
`;

const SubTitle = styled.h4`
  font-size: 1.2rem;
  color: ${({ theme }) => theme.text};
  margin-bottom: 0.5rem;
`;

const Text = styled.p`
  font-size: 1rem;
  color: ${({ theme }) => theme.textSecondary};
  line-height: 1.6;
  margin-bottom: 1rem;
`;

const List = styled.ul`
  padding-left: 1.5rem;
  margin-bottom: 1.5rem;
`;

const ListItem = styled.li`
  font-size: 1rem;
  color: ${({ theme }) => theme.textSecondary};
  line-height: 1.6;
  margin-bottom: 0.5rem;
`;

const Award = styled.div`
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: ${({ theme }) => theme.backgroundAlt};
  border-radius: 8px;
`;

const AwardTitle = styled.h5`
  font-size: 1.1rem;
  color: ${({ theme }) => theme.text};
  margin-bottom: 0.5rem;
`;

const AwardText = styled.p`
  font-size: 0.95rem;
  color: ${({ theme }) => theme.textSecondary};
`;

const About = () => {
  return (
    <PageSection id="about">
      <Container>
        <Header>
          <Title>关于我</Title>
          <Description>
            热爱技术创新的物联网工程专业学生，擅长开发与设计，具有良好的问题解决能力和团队协作精神。
          </Description>
        </Header>
        
        <Content>
          <ProfileSection>
            <Avatar src="/images/avatar.jpg" alt="Tully" />
            <Name>Tully</Name>
            <JobTitle>技术开发者</JobTitle>
            
            <ContactInfo>
              <ContactItem>
                <Icon>📧</Icon> <EMAIL>
              </ContactItem>
              <ContactItem>
                <Icon>🔗</Icon> https://github.com/Tully-L
              </ContactItem>
            </ContactInfo>
          </ProfileSection>
          
          <InfoSection>
            <InfoBlock>
              <SectionTitle>教育背景</SectionTitle>
              <SubTitle>**大学 | 物联网工程</SubTitle>
              <Text>2022.09—2026.06</Text>
              <Text><strong>主修课程</strong>：Web开发技术、数据结构与算法设计、嵌入式开发、数据库原理与应用</Text>
            </InfoBlock>
            
            <InfoBlock>
              <SectionTitle>个人简介</SectionTitle>
              <Text>
                我是一名对技术开发充满热情的物联网工程专业学生，在校期间系统学习了软件开发技术，熟练掌握多种编程语言和框架，
                具备独立开发应用的能力。我注重用户体验，善于运用设计思维创造直观、友好的解决方案。
              </Text>
              <Text>
                除技术能力外，我还积极参与校园活动和社会实践，担任**大学小球协会会长，组织各类体育活动；
                同时热爱阅读，连续三年获得阅读文化节"阅读之星"称号。我相信终身学习的价值，
                希望能够在科技行业不断成长，为用户创造有价值的产品。
              </Text>
            </InfoBlock>
            
            <InfoBlock>
              <SectionTitle>荣誉与证书</SectionTitle>
              
              <SubTitle>技能证书</SubTitle>
              <List>
                <ListItem>工业互联网平台开发工程师（初级）</ListItem>
                <ListItem>CET-6（英语六级）</ListItem>
                <ListItem>普通话国家二级甲等证书</ListItem>
              </List>
              
              <SubTitle>获奖经历</SubTitle>
              <Award>
                <AwardTitle>中国大学生计算机设计大赛省二</AwardTitle>
                <AwardText>2025.5</AwardText>
              </Award>
              <Award>
                <AwardTitle>蓝桥杯Web应用开发大学组三等奖</AwardTitle>
                <AwardText>2024.4</AwardText>
              </Award>
              <Award>
                <AwardTitle>阅读文化节"阅读之星"</AwardTitle>
                <AwardText>连续三年2023-2025</AwardText>
              </Award>
              
              <SubTitle>校园经历</SubTitle>
              <List>
                <ListItem>担任**大学小球协会会长（2024.9-至今）</ListItem>
                <ListItem>获院级三好学生（2023.11）</ListItem>
                <ListItem>"三下乡"社会实践活动优秀个人奖（2023.12）</ListItem>
              </List>
            </InfoBlock>
          </InfoSection>
        </Content>
      </Container>
    </PageSection>
  );
};

export default About; 