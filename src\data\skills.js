const skills = [
  {
    category: '开发技能',
    items: [
      {
        name: 'React',
        level: 85,
        icon: 'react',
        description: '使用React开发响应式应用，熟悉组件化开发、状态管理和Hooks',
      },
      {
        name: 'Vue.js',
        level: 80,
        icon: 'vue',
        description: '熟练使用Vue.js及相关生态，包括Vue Router、Vuex、Vue CLI等',
      },
      {
        name: 'TypeScript',
        level: 75,
        icon: 'typescript',
        description: '在项目中使用TypeScript提高代码质量和开发效率，熟悉类型系统',
      },
      {
        name: 'Less/CSS3',
        level: 90,
        icon: 'css',
        description: '精通CSS3和Less预处理器，能够实现复杂的响应式布局和动画效果',
      },
      {
        name: 'HTML5',
        level: 95,
        icon: 'html',
        description: '熟练使用HTML5新特性，如Canvas、LocalStorage、语义化标签等',
      },
    ]
  },
  {
    category: '技术框架',
    items: [
      {
        name: 'Uniapp',
        level: 85,
        icon: 'uniapp',
        description: '使用Uniapp开发跨平台应用，一次开发多端部署',
      },
      {
        name: 'Node.js',
        level: 70,
        icon: 'nodejs',
        description: '使用Node.js开发后端服务和API，熟悉异步编程和模块化',
      },
      {
        name: 'Express',
        level: 65,
        icon: 'express',
        description: '使用Express搭建Web服务器和RESTful API',
      },
      {
        name: 'Flask',
        level: 60,
        icon: 'flask',
        description: '使用Flask快速开发Python Web应用和API',
      },
      {
        name: 'MySQL',
        level: 75,
        icon: 'mysql',
        description: '熟练使用MySQL进行数据库设计和优化，能够编写复杂查询',
      },
    ]
  },
  {
    category: '综合能力',
    items: [
      {
        name: '微信小程序',
        level: 90,
        icon: 'wechat',
        description: '开发过多个微信小程序，熟悉小程序开发流程和API',
      },
      {
        name: 'STM32/ESP32',
        level: 75,
        icon: 'embedded',
        description: '熟悉嵌入式开发，能够使用STM32/ESP32实现物联网应用',
      },
      {
        name: 'Git',
        level: 85,
        icon: 'git',
        description: '熟练使用Git进行版本控制，包括分支管理、合并冲突解决等',
      },
      {
        name: '设计思维',
        level: 80,
        icon: 'design',
        description: '具备良好的UI/UX设计感，能够实现美观且用户友好的界面',
      },
    ]
  }
];

export default skills; 