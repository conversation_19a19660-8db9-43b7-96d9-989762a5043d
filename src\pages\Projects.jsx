import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import ProjectCard from '../components/ProjectCard';
import projects from '../data/projects';
import PageSection from '../components/PageSection';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  color: ${({ theme }) => theme.text};
  margin-bottom: 1rem;
  background: linear-gradient(
    to right,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
`;

const Description = styled.p`
  font-size: 1.1rem;
  color: ${({ theme }) => theme.textSecondary};
  max-width: 700px;
  margin: 0 auto;
`;

const FilterContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
`;

const FilterButton = styled.button`
  background-color: ${({ $active, theme }) => ($active ? theme.accent : theme.backgroundAlt)};
  color: ${({ $active, theme }) => ($active ? '#fff' : theme.text)};
  border: none;
  padding: 0.7rem 1.5rem;
  border-radius: 30px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${({ $active, theme }) => ($active ? theme.accentDark : theme.backgroundLight)};
    transform: translateY(-2px);
  }
`;

const ProjectsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const EmptyMessage = styled.p`
  text-align: center;
  font-size: 1.1rem;
  color: ${({ theme }) => theme.textSecondary};
  margin-top: 3rem;
`;

const Projects = () => {
  const [filter, setFilter] = useState('all');
  
  const filteredProjects = 
    filter === 'all' 
      ? projects 
      : projects.filter(project => project.category === filter);
      
  const categories = [
    { id: 'all', name: '全部项目' },
    { id: 'web', name: 'Web开发' },
    { id: 'miniprogram', name: '小程序' },
    { id: 'ai', name: 'AI项目' },
    { id: 'embedded', name: '嵌入式开发' }
  ];

  return (
    <PageSection id="projects">
      <Container>
        <Header>
          <Title>项目集</Title>
          <Description>
            以下是我参与开发的项目，涵盖Web、小程序、嵌入式等不同领域。每个项目都展示了我的技术能力和解决问题的思路。
          </Description>
        </Header>
        
        <FilterContainer>
          {categories.map(category => (
            <FilterButton
              key={category.id}
              $active={filter === category.id}
              onClick={() => setFilter(category.id)}
            >
              {category.name}
            </FilterButton>
          ))}
        </FilterContainer>
        
        {filteredProjects.length > 0 ? (
          <ProjectsGrid>
            {filteredProjects.map(project => (
              <ProjectCard key={project.id} project={project} />
            ))}
          </ProjectsGrid>
        ) : (
          <EmptyMessage>没有找到符合条件的项目</EmptyMessage>
        )}
      </Container>
    </PageSection>
  );
};

export default Projects; 