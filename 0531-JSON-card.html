<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON名片 - Tully</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        
        .card-container {
            position: relative;
            width: 550px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.4), 0 0 0 6px #2a2a2a;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .card-window {
            background-color: #1e1e1e;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .window-header {
            background-color: #252525;
            padding: 10px 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #333;
        }
        
        .title {
            color: #fff;
            text-align: center;
            flex-grow: 1;
            font-size: 16px;
        }
        
        .buttons {
            display: flex;
            gap: 8px;
        }
        
        .button {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .red { background-color: #ff5f56; }
        .yellow { background-color: #ffbd2e; }
        .green { background-color: #27c93f; }
        
        .window-controls {
            display: flex;
            gap: 15px;
            color: #888;
            font-size: 14px;
        }
        
        .minimize, .maximize, .close {
            user-select: none;
        }
        
        .minimize:after {
            content: "—";
        }
        
        .maximize:after {
            content: "□";
        }
        
        .close:after {
            content: "X";
        }
        
        .window-content {
            padding: 20px;
            color: #d4d4d4;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .line-numbers {
            color: #858585;
            text-align: right;
            padding-right: 10px;
            user-select: none;
            min-width: 20px;
        }
        
        .code-area {
            display: flex;
        }
        
        .code-content {
            flex-grow: 1;
            white-space: nowrap;
        }
        
        .brace { color: #d4d4d4; }
        .property { color: #9cdcfe; }
        .string { color: #ce9178; }
        .colon { color: #d4d4d4; }
        .comma { color: #d4d4d4; }
        
        .watermark {
            position: absolute;
            bottom: 10px;
            right: 10px;
            opacity: 0.7;
            color: #aaa;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .wechat-icon {
            width: 16px;
            height: 16px;
            background-color: #aaa;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="card-container">
        <div class="card-window">
            <div class="window-header">
                <div class="buttons">
                    <div class="button red"></div>
                    <div class="button yellow"></div>
                    <div class="button green"></div>
                </div>
                <div class="title">Business Card.json</div>
                <div class="window-controls">
                    <div class="minimize"></div>
                    <div class="maximize"></div>
                    <div class="close"></div>
                </div>
            </div>
            <div class="window-content">
                <div class="code-area">
                    <div class="line-numbers">
                        1<br>
                        2<br>
                        3<br>
                        4<br>
                        5<br>
                        6<br>
                        7<br>
                    </div>
                    <div class="code-content">
                        <span class="brace">{</span><br>
                        &nbsp;&nbsp;<span class="property">"Name"</span><span class="colon">:</span> <span class="string">"Tully"</span><span class="comma">,</span><br>
                        &nbsp;&nbsp;<span class="property">"Email"</span><span class="colon">:</span> <span class="string">"<EMAIL>"</span><span class="comma">,</span><br>
                        &nbsp;&nbsp;<span class="property">"Wechat"</span><span class="colon">:</span> <span class="string">"s17608325670"</span><span class="comma">,</span><br>
                        &nbsp;&nbsp;<span class="property">"Link"</span><span class="colon">:</span> <span class="string">"tully.top"</span><span class="comma">,</span><br>
                        &nbsp;&nbsp;<span class="property">"Github"</span><span class="colon">:</span> <span class="string">"@https://github.com/Tully-L"</span><br>
                        <span class="brace">}</span><br>
                    </div>
                </div>
            </div>
        </div>
        <div class="watermark">
            <div>💙</div>
            <span>Tully</span>
        </div>
    </div>
</body>
</html>