import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';
import { motion, useAnimation, useInView } from 'framer-motion';

const SectionContainer = styled(motion.section)`
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 80px 20px 20px;
  position: relative;
  scroll-snap-align: start;
  
  @media (max-width: 768px) {
    padding: 60px 15px 15px;
    min-height: 90vh;
  }
`;

const ContentWrapper = styled.div`
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  z-index: 2;
`;

// 页面动画变体
const pageVariants = {
  hidden: {
    opacity: 0,
    y: 20
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

const PageSection = ({ children, id, className }) => {
  const ref = useRef(null);
  // 更激进的预加载设置
  const isInView = useInView(ref, { 
    once: false, 
    amount: 0.05,
    rootMargin: "150% 0px -5% 0px"
  });
  const controls = useAnimation();
  
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);

  return (
    <SectionContainer
      id={id}
      className={className}
      ref={ref}
      variants={pageVariants}
      initial="hidden"
      animate={controls}
    >
      <ContentWrapper>
        {children}
      </ContentWrapper>
    </SectionContainer>
  );
};

export default PageSection; 