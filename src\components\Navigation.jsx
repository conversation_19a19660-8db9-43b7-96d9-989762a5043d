import React, { useState, useEffect, useContext } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { ThemeContext } from '../context/ThemeContext';

const NavContainer = styled.nav`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: ${({ theme }) => theme.navBg};
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
`;

const NavContent = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
  
  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const Logo = styled.a`
  font-size: 1.5rem;
  font-weight: 700;
  color: ${({ theme }) => theme.text};
  display: flex;
  align-items: center;
  cursor: pointer;
  
  span {
    background: linear-gradient(
      to right,
      ${({ theme }) => theme.gradientStart},
      ${({ theme }) => theme.gradientEnd}
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-left: 0.5rem;
  }
`;

const NavLinks = styled.div`
  display: flex;
  gap: 2rem;
  
  @media (max-width: 768px) {
    display: none;
  }
`;

const NavLink = styled.a`
  color: ${({ theme }) => theme.text};
  font-weight: 500;
  position: relative;
  cursor: pointer;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(
      to right,
      ${({ theme }) => theme.gradientStart},
      ${({ theme }) => theme.gradientEnd}
    );
    transition: width 0.3s ease;
  }
  
  &:hover::after,
  &.active::after {
    width: 100%;
  }
`;

const MobileMenuButton = styled.button`
  display: none;
  font-size: 1.5rem;
  color: ${({ theme }) => theme.text};
  
  @media (max-width: 768px) {
    display: block;
  }
`;

const MobileMenu = styled(motion.div)`
  position: fixed;
  top: 0;
  right: 0;
  width: 70%;
  height: 100vh;
  background-color: ${({ theme }) => theme.backgroundAlt};
  z-index: 1001;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  
  @media (min-width: 769px) {
    display: none;
  }
`;

const MobileNavLink = styled.a`
  color: ${({ theme }) => theme.text};
  font-size: 1.5rem;
  font-weight: 500;
  cursor: pointer;
  
  &.active {
    color: ${({ theme }) => theme.primary};
  }
`;

const ThemeToggle = styled.button`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ theme }) => theme.backgroundAlt};
  color: ${({ theme }) => theme.text};
  transition: all 0.3s ease;
  
  &:hover {
    transform: rotate(30deg);
  }
`;

const Overlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  
  @media (min-width: 769px) {
    display: none;
  }
`;

const Navigation = ({ activeSection, onNavClick }) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const { theme, toggleTheme } = useContext(ThemeContext);
  
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);
  
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };
  
  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
  };
  
  const handleNavLinkClick = (sectionId) => {
    onNavClick(sectionId);
    closeMobileMenu();
  };
  
  return (
    <NavContainer style={{ boxShadow: scrolled ? '0 2px 10px rgba(0, 0, 0, 0.1)' : 'none' }}>
      <NavContent>
        <Logo onClick={() => handleNavLinkClick('home')}>
          <span>Tully</span>
        </Logo>
        
        <NavLinks>
          <NavLink 
            onClick={() => handleNavLinkClick('home')}
            className={activeSection === 'home' ? 'active' : ''}
          >
            首页
          </NavLink>
          <NavLink 
            onClick={() => handleNavLinkClick('projects')}
            className={activeSection === 'projects' ? 'active' : ''}
          >
            项目
          </NavLink>
          <NavLink 
            onClick={() => handleNavLinkClick('skills')}
            className={activeSection === 'skills' ? 'active' : ''}
          >
            技能
          </NavLink>
          <NavLink 
            onClick={() => handleNavLinkClick('about')}
            className={activeSection === 'about' ? 'active' : ''}
          >
            关于
          </NavLink>
          <NavLink 
            onClick={() => handleNavLinkClick('contact')}
            className={activeSection === 'contact' ? 'active' : ''}
          >
            联系
          </NavLink>
        </NavLinks>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <ThemeToggle onClick={toggleTheme}>
            {theme === 'sun' ? '☀️' : theme === 'moon' ? '🌙' : '⭐'}
          </ThemeToggle>
          
          <MobileMenuButton onClick={toggleMobileMenu}>
            {mobileMenuOpen ? '✕' : '☰'}
          </MobileMenuButton>
        </div>
      </NavContent>
      
      {mobileMenuOpen && (
        <Overlay 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={closeMobileMenu}
        />
      )}
      
      <MobileMenu
        initial={{ x: '100%' }}
        animate={{ x: mobileMenuOpen ? 0 : '100%' }}
        transition={{ type: 'tween', duration: 0.3 }}
      >
        <MobileNavLink 
          onClick={() => handleNavLinkClick('home')}
          className={activeSection === 'home' ? 'active' : ''}
        >
          首页
        </MobileNavLink>
        <MobileNavLink 
          onClick={() => handleNavLinkClick('projects')}
          className={activeSection === 'projects' ? 'active' : ''}
        >
          项目
        </MobileNavLink>
        <MobileNavLink 
          onClick={() => handleNavLinkClick('skills')}
          className={activeSection === 'skills' ? 'active' : ''}
        >
          技能
        </MobileNavLink>
        <MobileNavLink 
          onClick={() => handleNavLinkClick('about')}
          className={activeSection === 'about' ? 'active' : ''}
        >
          关于
        </MobileNavLink>
        <MobileNavLink 
          onClick={() => handleNavLinkClick('contact')}
          className={activeSection === 'contact' ? 'active' : ''}
        >
          联系
        </MobileNavLink>
      </MobileMenu>
    </NavContainer>
  );
};

export default Navigation; 