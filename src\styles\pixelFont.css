/* 像素风字体样式 */
@import url('https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap');

/* 全局像素风格 */
.pixel-art {
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
}

.pixel-font {
  font-family: 'Press Start 2P', cursive;
  letter-spacing: 1px;
  line-height: 1.5;
  text-shadow: 2px 2px 0 rgba(0, 0, 0, 0.7);
}

/* 像素风按钮 */
.pixel-button {
  font-family: 'Press Start 2P', cursive;
  background-color: #333;
  color: white;
  border: 4px solid white;
  box-shadow: 0 4px 0 #222, 4px 4px 0 rgba(0, 0, 0, 0.5);
  padding: 10px 20px;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.1s;
  image-rendering: pixelated;
  position: relative;
  text-shadow: 2px 2px 0 rgba(0, 0, 0, 0.7);
}

.pixel-button:hover {
  transform: translateY(2px);
  box-shadow: 0 2px 0 #222, 2px 2px 0 rgba(0, 0, 0, 0.5);
  background-color: #444;
}

.pixel-button:active {
  transform: translateY(4px);
  box-shadow: none;
}

/* 像素风对话框 */
.pixel-dialog {
  font-family: 'Press Start 2P', cursive;
  background-color: #000;
  color: white;
  border: 4px solid white;
  padding: 16px;
  box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.5);
  position: relative;
}

.pixel-dialog::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    transparent,
    transparent 2px,
    rgba(255, 255, 255, 0.03) 2px,
    rgba(255, 255, 255, 0.03) 4px
  );
  pointer-events: none;
}

/* 像素风标题 */
.pixel-title {
  font-family: 'Press Start 2P', cursive;
  text-transform: uppercase;
  text-shadow: 3px 3px 0 #000;
  position: relative;
  display: inline-block;
}

.pixel-title::after {
  content: '';
  position: absolute;
  left: -5px;
  right: -5px;
  bottom: -5px;
  height: 4px;
  background: white;
  box-shadow: 2px 2px 0 rgba(0, 0, 0, 0.7);
}

/* 像素风容器 */
.pixel-container {
  border: 4px solid white;
  background-color: #000;
  image-rendering: pixelated;
  box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.5);
  position: relative;
}

.pixel-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    transparent,
    transparent 2px,
    rgba(255, 255, 255, 0.03) 2px,
    rgba(255, 255, 255, 0.03) 4px
  );
  pointer-events: none;
}

/* 像素风格子效果 */
.pixel-grid {
  position: relative;
}

.pixel-grid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 16px 16px;
  pointer-events: none;
  z-index: 1;
}

/* 像素风闪烁动画 */
@keyframes pixel-flicker {
  0% { opacity: 1; }
  2% { opacity: 0.8; }
  4% { opacity: 1; }
  8% { opacity: 0.9; }
  70% { opacity: 1; }
  72% { opacity: 0.8; }
  74% { opacity: 1; }
}

.pixel-flicker {
  animation: pixel-flicker 4s infinite;
}

/* 打字机光标动画 */
@keyframes cursor-blink {
  0% { opacity: 0; }
  50% { opacity: 1; }
  100% { opacity: 0; }
}

.cursor {
  display: inline-block;
  width: 0.6em;
  height: 1em;
  background: white;
  margin-left: 2px;
  animation: cursor-blink 0.8s infinite;
  vertical-align: middle;
}

/* 复古屏幕效果 */
.retro-screen {
  position: relative;
  overflow: hidden;
}

.retro-screen::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(
      rgba(18, 16, 16, 0) 50%, 
      rgba(0, 0, 0, 0.25) 50%
    ),
    linear-gradient(
      90deg, 
      rgba(255, 0, 0, 0.06), 
      rgba(0, 255, 0, 0.02), 
      rgba(0, 0, 255, 0.06)
    );
  background-size: 100% 2px, 3px 100%;
  pointer-events: none;
  opacity: 0.2;
  z-index: 2;
} 