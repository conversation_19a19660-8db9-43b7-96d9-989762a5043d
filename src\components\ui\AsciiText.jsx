import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

const AsciiContainer = styled.div`
  font-family: monospace;
  font-size: 1.5rem;
  color: ${({ theme }) => theme.primary};
  margin-bottom: 1rem;
  line-height: 1.4;
  
  @media (max-width: 768px) {
    font-size: 1.2rem;
  }
`;

const AsciiText = ({ text, speed = 50 }) => {
  const [displayText, setDisplayText] = useState('');
  const [isComplete, setIsComplete] = useState(false);
  
  useEffect(() => {
    let currentText = '';
    let currentIndex = 0;
    let scrambleInterval;
    
    // 随机ASCII字符生成函数
    const getRandomChar = () => {
      const chars = '!@#$%^&*()_+-=[]{}|;:,.<>?/~`';
      return chars[Math.floor(Math.random() * chars.length)];
    };
    
    // 开始ASCII转换动画
    const startScrambleEffect = () => {
      // 重置状态
      setDisplayText('');
      currentText = '';
      currentIndex = 0;
      setIsComplete(false);
      
      scrambleInterval = setInterval(() => {
        if (currentIndex >= text.length) {
          clearInterval(scrambleInterval);
          setIsComplete(true);
          return;
        }
        
        // 构建当前文本，包括已解析部分和正在解析部分
        let newText = text.substring(0, currentIndex);
        
        // 为当前字符添加随机效果
        if (currentIndex < text.length) {
          const scrambleLength = Math.min(5, text.length - currentIndex);
          for (let i = 0; i < scrambleLength; i++) {
            if (i === 0 && Math.random() > 0.5) {
              // 50%的概率显示正确字符
              newText += text[currentIndex];
            } else {
              newText += getRandomChar();
            }
          }
        }
        
        setDisplayText(newText);
        
        // 每隔几次迭代增加一个已解析字符
        if (Math.random() > 0.7) {
          currentIndex++;
        }
      }, speed);
    };
    
    startScrambleEffect();
    
    return () => {
      clearInterval(scrambleInterval);
    };
  }, [text, speed]);
  
  return <AsciiContainer>{displayText}</AsciiContainer>;
};

export default AsciiText; 