<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON名片 - Tully</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #eef2ff;
            font-weight: bold;
        }
        
        .card-container {
            position: relative;
            width: 2200px;
            box-shadow: 0 40px 80px rgba(23, 64, 196, 0.4), 0 0 0 12px #0d2987;
            border-radius: 20px;
            overflow: hidden;
        }
        
        .card-window {
            background-color: #1740c4;
            border-radius: 20px;
            overflow: hidden;
        }
        
        .window-header {
            background-color: #2855d9;
            padding: 30px 40px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 4px solid #4f73e8;
        }
        
        .title {
            color: #ffffff;
            text-align: center;
            flex-grow: 1;
            font-size: 64px;
            font-weight: bold;
        }
        
        .buttons {
            display: flex;
            gap: 32px;
        }
        
        .button {
            width: 48px;
            height: 48px;
            border-radius: 50%;
        }
        
        .red { background-color: #ff5f56; }
        .yellow { background-color: #ffbd2e; }
        .green { background-color: #27c93f; }
        
        .window-controls {
            display: flex;
            gap: 40px;
            color: #a3b5ff;
            font-size: 56px;
            font-weight: bold;
        }
        
        .minimize, .maximize, .close {
            user-select: none;
        }
        
        .minimize:after {
            content: "—";
        }
        
        .maximize:after {
            content: "□";
        }
        
        .close:after {
            content: "X";
        }
        
        .window-content {
            padding: 60px 80px;
            color: #ffffff;
            font-size: 64px;
            line-height: 1.8;
            background-color: #1740c4;
            font-weight: bold;
        }
        
        .line-numbers {
            color: #8aa0ff;
            text-align: right;
            padding-right: 40px;
            user-select: none;
            min-width: 80px;
            font-weight: bold;
        }
        
        .code-area {
            display: flex;
            overflow-x: auto;
        }
        
        .code-content {
            flex-grow: 1;
            white-space: nowrap;
            max-width: none;
        }
        
        .code-line {
            white-space: nowrap;
            display: inline-block;
            margin-right: 0;
        }
        
        .repo-line {
            white-space: nowrap;
            display: inline-block;
            padding-left: 0;
            text-indent: 0;
        }
        
        .brace { 
            color: #ffffff; 
            font-weight: bold;
        }
        .property { 
            color: #c2d0ff; 
            font-weight: bold;
        }
        .string { 
            color: #e8edff; 
            font-weight: bold;
        }
        .colon { 
            color: #ffffff; 
            font-weight: bold;
        }
        .comma { 
            color: #ffffff; 
            font-weight: bold;
        }
        
        .watermark {
            position: absolute;
            bottom: 40px;
            right: 40px;
            opacity: 0.9;
            color: #a3b5ff;
            font-size: 48px;
            display: flex;
            align-items: center;
            gap: 20px;
            font-weight: bold;
        }
        
        .wechat-icon {
            width: 64px;
            height: 64px;
            background-color: #a3b5ff;
            border-radius: 12px;
        }
    </style>
</head>
<body>
    <div class="card-container">
        <div class="card-window">
            <div class="window-header">
                <div class="buttons">
                    <div class="button red"></div>
                    <div class="button yellow"></div>
                    <div class="button green"></div>
                </div>
                <div class="title">个人介绍.json</div>
                <div class="window-controls">
                    <div class="minimize"></div>
                    <div class="maximize"></div>
                    <div class="close"></div>
                </div>
            </div>
            <div class="window-content">
                <div class="code-area">
                    <div class="line-numbers">
                        1<br>
                        2<br>
                        3<br>
                        4<br>
                        5<br>
                        6<br>
                        7<br>
                    </div>
                    <div class="code-content">
                        <span class="brace">{</span><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;<span class="property">"Name"</span><span class="colon">:</span> <span class="string">"林雨"</span><span class="comma">,</span><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;<span class="property">"Major"</span><span class="colon">:</span> <span class="string">"物联网工程"</span><span class="comma">,</span><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;<span class="property">"Work"</span><span class="colon">:</span> <span class="string">"前端开发工程师"</span><span class="comma">,</span><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;<span class="property">"Repo"</span><span class="colon">:</span> <span class="string">"个人作品网站，微信小程序，智谋慧检系统"</span><span class="comma">,</span><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;<span class="property">"Link"</span><span class="colon">:</span> <span class="string">"https://tully.top/"</span><span class="comma">,</span><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;<span class="property">"Github"</span><span class="colon">:</span> <span class="string">"@https://github.com/Tully-L"</span><br>
                        <span class="brace">}</span><br>
                    </div>
                </div>
            </div>
        </div>
        <div class="watermark">
            <div style="font-size: 48px; font-weight: bold;">💙</div>
            <span>Tully</span>
        </div>
    </div>
</body>
</html>