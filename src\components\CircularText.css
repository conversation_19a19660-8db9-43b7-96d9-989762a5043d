/* 自定义回到顶部按钮的 CircularText 样式 */
.custom-circular-text.circular-text {
  width: 35px;
  height: 35px;
  color: ${({ theme }) => theme.text};
  opacity: 0.6;
}

.custom-circular-text.circular-text span {
  font-size: 8px;
  font-weight: 600;
  text-transform: uppercase;
}

@media (max-width: 768px) {
  .custom-circular-text.circular-text {
    width: 30px;
    height: 30px;
  }

  .custom-circular-text.circular-text span {
    font-size: 6px;
  }
} 