const interests = [
  {
    id: 1,
    title: '阅读',
    icon: '📚',
    description: '热爱阅读各类书籍，尤其是科技、心理学和历史题材。连续三年获得阅读文化节"阅读之星"称号。',
    items: [
      {
        name: '技术书籍',
        description: '《深入理解计算机系统》、《JavaScript高级程序设计》、《图解HTTP》等'
      },
      {
        name: '心理学',
        description: '《思考，快与慢》、《影响力》、《社会心理学》等'
      },
      {
        name: '历史',
        description: '《人类简史》、《枪炮、病菌与钢铁》、《明朝那些事儿》等'
      }
    ]
  },
  {
    id: 2,
    title: '球类运动',
    icon: '🏓',
    description: '担任大学小球协会会长，组织乒乓球、羽毛球等活动。喜欢通过运动缓解压力，增强团队协作能力。',
    items: [
      {
        name: '乒乓球',
        description: '业余爱好者，校内小比赛获得过名次'
      },
      {
        name: '羽毛球',
        description: '每周固定打羽毛球，曾组织院系间比赛'
      },
      {
        name: '篮球',
        description: '周末和朋友一起打球，锻炼身体'
      }
    ]
  },
  {
    id: 3,
    title: '技术分享',
    icon: '👨‍💻',
    description: '热衷于技术分享和知识传播，定期在社区分享学习笔记和项目经验。相信知识共享能促进个人成长。',
    items: [
      {
        name: '博客写作',
        description: '定期更新技术博客，记录学习心得和项目经验'
      },
      {
        name: '校内讲座',
        description: '组织过Web前端入门讲座，帮助新生了解编程'
      },
      {
        name: '开源贡献',
        description: '参与小型开源项目，提交PR和Bug修复'
      }
    ]
  },
  {
    id: 4,
    title: '社会实践',
    icon: '🌱',
    description: '积极参与社会实践活动，获得"三下乡"社会实践活动优秀个人奖。通过实践增强社会责任感和服务意识。',
    items: [
      {
        name: '支教活动',
        description: '参与乡村小学支教，教授编程启蒙课程'
      },
      {
        name: '环保志愿',
        description: '参与校园环保活动，推广垃圾分类知识'
      },
      {
        name: '技术帮扶',
        description: '为老年人提供智能手机使用培训，缩小数字鸿沟'
      }
    ]
  },
  {
    id: 5,
    title: '创意设计',
    icon: '🎨',
    description: '业余时间喜欢UI设计和平面设计，为项目提供美观的视觉效果。相信好的设计能提升用户体验。',
    items: [
      {
        name: 'UI设计',
        description: '自学Figma和Adobe XD，为自己的项目设计界面'
      },
      {
        name: '平面设计',
        description: '制作过社团海报和活动宣传材料'
      },
      {
        name: '动效设计',
        description: '学习CSS动画和SVG动画，提升网页交互体验'
      }
    ]
  }
];

export default interests; 