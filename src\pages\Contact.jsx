import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import PageSection from '../components/PageSection';
import { sendEmail, validateFormData } from '../services/emailService';

const ContactContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  
  @media (max-width: 768px) {
    padding: 0 1rem;
  }
`;

const ContactTitle = styled.h1`
  font-size: 3rem;
  margin-bottom: 2rem;
  background: linear-gradient(
    to right,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  
  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const ContactGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const ContactInfo = styled.div`
  h2 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
  }
  
  p {
    color: ${({ theme }) => theme.textSecondary};
    margin-bottom: 2rem;
    line-height: 1.8;
  }
`;

const ContactMethod = styled.div`
  margin-bottom: 1.5rem;
  
  h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: ${({ theme }) => theme.primary};
  }
`;

const ContactValue = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  
  p {
    color: ${({ theme }) => theme.text};
    margin: 0;
  }
  
  a {
    color: ${({ theme }) => theme.text};
    text-decoration: none;
    
    &:hover {
      color: ${({ theme }) => theme.primary};
    }
  }
`;

const CopyButton = styled.button`
  background-color: ${({ theme }) => theme.backgroundAlt};
  color: ${({ theme }) => theme.text};
  border: 1px solid ${({ theme }) => theme.border};
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: ${({ theme }) => theme.primary};
    color: white;
  }
  
  &:active {
    transform: translateY(1px);
  }
`;

const CopyMessage = styled(motion.span)`
  font-size: 0.8rem;
  color: ${({ theme }) => theme.accent};
  margin-left: 8px;
`;

const ContactForm = styled.form`
  background-color: ${({ theme }) => theme.cardBg};
  padding: 2rem;
  border-radius: 10px;
  box-shadow: ${({ theme }) => theme.shadow};
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    color: ${({ theme }) => theme.textSecondary};
  }
  
  input, textarea {
    width: 100%;
    padding: 0.8rem 1rem;
    background-color: ${({ theme }) => theme.backgroundAlt};
    border: 1px solid ${({ theme }) => theme.border};
    border-radius: 5px;
    color: ${({ theme }) => theme.text};
    font-family: inherit;
    
    &:focus {
      outline: none;
      border-color: ${({ theme }) => theme.primary};
    }
  }
  
  textarea {
    min-height: 150px;
    resize: vertical;
  }
`;

const SubmitButton = styled(motion.button)`
  padding: 0.8rem 2rem;
  background: linear-gradient(
    to right,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  color: white;
  border-radius: 30px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const FormMessage = styled.div`
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-weight: 500;

  &.success {
    background-color: #D1FAE5;
    color: #065F46;
    border: 1px solid #A7F3D0;
  }

  &.error {
    background-color: #FEE2E2;
    color: #991B1B;
    border: 1px solid #FECACA;
  }

  &.info {
    background-color: #DBEAFE;
    color: #1E40AF;
    border: 1px solid #93C5FD;
  }
`;

const ErrorMessage = styled.span`
  color: #DC2626;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
`;

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  
  const [copyStatus, setCopyStatus] = useState({
    email: false,
    wechat: false,
    github: false
  });

  const [formStatus, setFormStatus] = useState({
    isSubmitting: false,
    message: '',
    type: '' // 'success' | 'error' | ''
  });

  const [formErrors, setFormErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // 清除对应字段的错误信息
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    console.log('表单数据:', formData);

    // 验证表单数据
    const validation = validateFormData(formData);
    if (!validation.isValid) {
      setFormErrors(validation.errors);
      console.log('表单验证失败:', validation.errors);
      return;
    }

    // 清除错误信息
    setFormErrors({});

    // 设置提交状态
    setFormStatus({
      isSubmitting: true,
      message: '正在发送邮件...',
      type: 'info'
    });

    try {
      console.log('开始发送邮件...');
      // 发送邮件
      const result = await sendEmail(formData);
      console.log('邮件发送结果:', result);

      if (result.success) {
        setFormStatus({
          isSubmitting: false,
          message: result.message,
          type: 'success'
        });

        // 清空表单
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: ''
        });
      } else {
        setFormStatus({
          isSubmitting: false,
          message: result.message,
          type: 'error'
        });
      }
    } catch (error) {
      console.error('邮件发送异常:', error);
      setFormStatus({
        isSubmitting: false,
        message: '发送失败，请稍后重试或直接联系我。',
        type: 'error'
      });
    }

    // 5秒后清除状态消息
    setTimeout(() => {
      setFormStatus({
        isSubmitting: false,
        message: '',
        type: ''
      });
    }, 5000);
  };
  
  const copyToClipboard = (text, field) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopyStatus({ ...copyStatus, [field]: true });
      
      // 3秒后重置复制状态
      setTimeout(() => {
        setCopyStatus({ ...copyStatus, [field]: false });
      }, 3000);
    });
  };
  
  const contactInfo = {
    email: '<EMAIL>',
    wechat: 's17608325670',
    github: 'github.com/Tully-L'
  };
  
  return (
    <PageSection id="contact">
      <ContactContainer>
        <ContactTitle>联系我</ContactTitle>
        
        <ContactGrid>
          <ContactInfo>
            <h2>让我们取得联系</h2>
            <p>
              无论您有项目合作、咨询需求，还是只是想打个招呼，我都很乐意听取您的意见。
              填写表单或通过以下方式直接联系我。
            </p>
            
            <ContactMethod>
              <h3>邮箱</h3>
              <ContactValue>
                <p>{contactInfo.email}</p>
                <CopyButton onClick={() => copyToClipboard(contactInfo.email, 'email')}>
                  复制
                </CopyButton>
                {copyStatus.email && (
                  <CopyMessage
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    已复制!
                  </CopyMessage>
                )}
              </ContactValue>
            </ContactMethod>
            
            <ContactMethod>
              <h3>微信</h3>
              <ContactValue>
                <p>{contactInfo.wechat}</p>
                <CopyButton onClick={() => copyToClipboard(contactInfo.wechat, 'wechat')}>
                  复制
                </CopyButton>
                {copyStatus.wechat && (
                  <CopyMessage
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    已复制!
                  </CopyMessage>
                )}
              </ContactValue>
            </ContactMethod>
            
            <ContactMethod>
              <h3>GitHub</h3>
              <ContactValue>
                <a href="https://github.com/Tully-L" target="_blank" rel="noopener noreferrer">
                  {contactInfo.github}
                </a>
                <CopyButton onClick={() => copyToClipboard(contactInfo.github, 'github')}>
                  复制
                </CopyButton>
                {copyStatus.github && (
                  <CopyMessage
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    已复制!
                  </CopyMessage>
                )}
              </ContactValue>
            </ContactMethod>
          </ContactInfo>
          
          <ContactForm onSubmit={handleSubmit}>
            {formStatus.message && (
              <FormMessage className={formStatus.type}>
                {formStatus.message}
              </FormMessage>
            )}

            <FormGroup>
              <label htmlFor="name">姓名</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />
              {formErrors.name && <ErrorMessage>{formErrors.name}</ErrorMessage>}
            </FormGroup>
            
            <FormGroup>
              <label htmlFor="email">邮箱</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
              />
              {formErrors.email && <ErrorMessage>{formErrors.email}</ErrorMessage>}
            </FormGroup>

            <FormGroup>
              <label htmlFor="subject">主题</label>
              <input
                type="text"
                id="subject"
                name="subject"
                value={formData.subject}
                onChange={handleChange}
                required
              />
              {formErrors.subject && <ErrorMessage>{formErrors.subject}</ErrorMessage>}
            </FormGroup>

            <FormGroup>
              <label htmlFor="message">消息</label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                required
              ></textarea>
              {formErrors.message && <ErrorMessage>{formErrors.message}</ErrorMessage>}
            </FormGroup>
            
            <SubmitButton
              type="submit"
              disabled={formStatus.isSubmitting}
              whileHover={!formStatus.isSubmitting ? { y: -5 } : {}}
              whileTap={!formStatus.isSubmitting ? { y: 0 } : {}}
            >
              {formStatus.isSubmitting ? '发送中...' : '发送消息'}
            </SubmitButton>
          </ContactForm>
        </ContactGrid>
      </ContactContainer>
    </PageSection>
  );
};

export default Contact; 