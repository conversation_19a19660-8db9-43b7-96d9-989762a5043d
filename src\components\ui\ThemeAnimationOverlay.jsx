import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';

// 全屏动画遮罩层
const OverlayContainer = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform, opacity;
`;

// GIF 动画容器
const GifAnimationContainer = styled(motion.div)`
  width: 100%;
  height: 100%;
  background-image: url(${props => props.gifUrl});
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  image-rendering: auto;
  image-rendering: crisp-edges;
  image-rendering: -webkit-optimize-contrast;
  will-change: transform, opacity;
  transform: translateZ(0);
`;

// 圆形扩散动画容器
const CircleAnimationContainer = styled(motion.div)`
  position: absolute;
  border-radius: 50%;
  background: ${({ currentTheme }) => {
    switch(currentTheme) {
      case 'sun': return 'linear-gradient(135deg, #FFD700, #FFA500)';
      case 'moon': return 'linear-gradient(135deg, #4cc9f0, #7209b7)';
      case 'star': return 'linear-gradient(135deg, #ffd700, #87ceeb)';
      default: return 'linear-gradient(135deg, #ffd700, #87ceeb)';
    }
  }};
  filter: blur(2px);
`;

// 模糊圆形动画容器
const CircleBlurContainer = styled(CircleAnimationContainer)`
  filter: blur(8px);
  opacity: 0.8;
`;

const ThemeAnimationOverlay = ({ 
  variant = "gif", 
  gifUrl, 
  currentTheme, 
  buttonPosition,
  isVisible,
  onAnimationComplete 
}) => {
  
  // 计算动画的起始和结束状态
  const getAnimationProps = () => {
    const screenDiagonal = Math.sqrt(
      Math.pow(window.innerWidth, 2) + Math.pow(window.innerHeight, 2)
    );
    
    const baseProps = {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 },
      transition: { duration: 0.6 }
    };

    switch(variant) {
      case "gif":
        return {
          ...baseProps,
          initial: { ...baseProps.initial, scale: 0 },
          animate: { ...baseProps.animate, scale: 1 },
          exit: { ...baseProps.exit, scale: 0 },
          transition: {
            duration: 2.0,
            ease: "easeInOut"
          }
        };

      case "circle":
        return {
          ...baseProps,
          initial: { 
            scale: 0,
            x: buttonPosition.x - window.innerWidth / 2,
            y: buttonPosition.y - window.innerHeight / 2
          },
          animate: { 
            scale: screenDiagonal / 50,
            x: 0,
            y: 0
          },
          exit: { scale: 0, opacity: 0 },
          transition: { 
            duration: 0.8,
            ease: "easeInOut"
          }
        };

      case "circle-blur":
        return {
          ...baseProps,
          initial: { 
            scale: 0,
            x: buttonPosition.x - window.innerWidth / 2,
            y: buttonPosition.y - window.innerHeight / 2
          },
          animate: { 
            scale: screenDiagonal / 40,
            x: 0,
            y: 0
          },
          exit: { scale: 0, opacity: 0 },
          transition: { 
            duration: 1.0,
            ease: "easeInOut"
          }
        };

      default:
        return baseProps;
    }
  };

  const animationProps = getAnimationProps();

  // 渲染不同类型的动画内容
  const renderAnimationContent = () => {
    switch(variant) {
      case "gif":
        return (
          <GifAnimationContainer
            gifUrl={gifUrl}
            {...animationProps}
            onAnimationComplete={onAnimationComplete}
          />
        );

      case "circle":
        return (
          <CircleAnimationContainer
            currentTheme={currentTheme}
            {...animationProps}
            onAnimationComplete={onAnimationComplete}
            style={{
              width: '50px',
              height: '50px',
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)'
            }}
          />
        );

      case "circle-blur":
        return (
          <CircleBlurContainer
            currentTheme={currentTheme}
            {...animationProps}
            onAnimationComplete={onAnimationComplete}
            style={{
              width: '50px',
              height: '50px',
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)'
            }}
          />
        );

      default:
        return null;
    }
  };

  if (!isVisible) return null;

  return (
    <OverlayContainer
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.6 }}
    >
      {renderAnimationContent()}
    </OverlayContainer>
  );
};

export default ThemeAnimationOverlay;
