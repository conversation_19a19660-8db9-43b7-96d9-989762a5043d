import React, { useState, useContext, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { AnimatePresence } from 'framer-motion';
import { ThemeContext } from '../../context/ThemeContext';
import ThemeAnimationOverlay from './ThemeAnimationOverlay';

// 主题切换按钮样式
const ThemeToggle = styled.button`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ theme }) => theme.backgroundAlt};
  color: ${({ theme }) => theme.text};
  transition: all 0.3s ease;
  position: relative;
  z-index: 1001;
  
  &:hover {
    transform: rotate(30deg);
  }
`;



const ThemeToggleButton = ({ 
  variant = "gif", 
  url = "https://media1.giphy.com/media/v1.Y2lkPTc5MGI3NjExbmlrZHpvcjA4eTcyMTl0eTBjeTZvdHU0a2dpZ3NwaGlmYWJtOG5xdiZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9cw/l0IybvANIIMMOHrUY/giphy.gif",
  start = "center",
  showLabel = false 
}) => {
  const { theme, toggleTheme } = useContext(ThemeContext);
  const [isAnimating, setIsAnimating] = useState(false);
  const [gifLoaded, setGifLoaded] = useState(false);
  const buttonRef = useRef(null);

  // 预加载 GIF
  useEffect(() => {
    if (variant === "gif" && url) {
      const img = new Image();
      img.onload = () => {
        setGifLoaded(true);
        console.log('Theme animation GIF loaded successfully');
      };
      img.onerror = () => {
        setGifLoaded(false);
        console.warn('Theme animation GIF failed to load, using fallback animation');
      };
      img.src = url;
    }
  }, [variant, url]);

  // 获取按钮位置用于动画起始点
  const getButtonPosition = () => {
    if (!buttonRef.current) return { x: window.innerWidth / 2, y: window.innerHeight / 2 };
    
    const rect = buttonRef.current.getBoundingClientRect();
    return {
      x: rect.left + rect.width / 2,
      y: rect.top + rect.height / 2
    };
  };

  // 处理主题切换
  const handleThemeToggle = async () => {
    if (isAnimating) return;

    try {
      setIsAnimating(true);

      // 延迟切换主题，让动画先开始
      setTimeout(() => {
        toggleTheme();
      }, 400); // 动画中途切换主题

      // 动画结束后重置状态
      setTimeout(() => {
        setIsAnimating(false);
      }, 1200); // 总动画时长

    } catch (error) {
      console.error('Theme toggle animation error:', error);
      setIsAnimating(false);
      // 如果动画失败，直接切换主题
      toggleTheme();
    }
  };

  // 获取当前主题图标
  const getThemeIcon = () => {
    switch(theme) {
      case 'sun': return '☀️';
      case 'moon': return '🌙';
      case 'star': return '⭐';
      default: return '⭐';
    }
  };

  return (
    <>
      <ThemeToggle ref={buttonRef} onClick={handleThemeToggle}>
        {getThemeIcon()}
      </ThemeToggle>

      {/* 动画遮罩层 */}
      <AnimatePresence>
        {isAnimating && (
          <ThemeAnimationOverlay
            variant={variant}
            gifUrl={variant === "gif" && gifLoaded ? url : null}
            currentTheme={theme}
            buttonPosition={getButtonPosition()}
            isVisible={isAnimating}
            onAnimationComplete={() => setIsAnimating(false)}
          />
        )}
      </AnimatePresence>

      {/* 开发调试标签 */}
      {showLabel && (
        <div style={{ 
          position: 'absolute', 
          bottom: '-30px', 
          left: '50%', 
          transform: 'translateX(-50%)',
          fontSize: '10px',
          color: '#666'
        }}>
          {variant} - {start}
        </div>
      )}
    </>
  );
};

export default ThemeToggleButton;
