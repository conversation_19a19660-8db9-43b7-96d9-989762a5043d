// 定义太阳主题 (亮色模式 - 温暖的白天色彩)
export const sunTheme = {
  background: 'linear-gradient(135deg, #0F4C81 0%, #1A237E 100%)',
  backgroundAlt: '#F8F9FA',
  text: '#2C3E50',
  textSecondary: '#5D6D7E',
  primary: '#3498DB',
  secondary: '#E67E22',
  accent: '#2980B9',
  border: '#BDC3C7',
  shadow: 'rgba(0, 0, 0, 0.1)',
  gradientStart: '#00BFFF',
  gradientEnd: '#191970',
};

// 定义月亮主题 (暗色模式 - 普通黑色背景)
export const moonTheme = {
  background: '#121212',
  backgroundAlt: '#1e1e1e',
  text: '#f8f9fa',
  textSecondary: '#adb5bd',
  primary: '#4cc9f0',
  secondary: '#7209b7',
  accent: '#f72585',
  border: '#343a40',
  shadow: 'rgba(0, 0, 0, 0.3)',
  gradientStart: '#4cc9f0',
  gradientEnd: '#f72585',
};

// 定义星星主题 (带3D星空背景)
export const starTheme = {
  background: '#000000',
  backgroundAlt: '#1a1a2e',
  text: '#f8f9fa',
  textSecondary: '#adb5bd',
  primary: '#ffd700',
  secondary: '#87ceeb',
  accent: '#ff6347',
  border: '#2a2a3e',
  shadow: 'rgba(255, 215, 0, 0.2)',
  gradientStart: '#ffd700',
  gradientEnd: '#87ceeb',
}; 

// Export aliases for backward compatibility
export const lightTheme = sunTheme;
export const darkTheme = moonTheme;