import { createGlobalStyle } from 'styled-components';

const GlobalStyle = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  html {
    scroll-behavior: smooth;
    scroll-snap-type: y mandatory;
    overflow-x: hidden;
  }
  
  body {
    font-family: "微软雅黑", "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
    background-color: ${({ theme }) => theme.background};
    color: ${({ theme }) => theme.text};
    line-height: 1.6;
    overflow-x: hidden;
  }
  
  main {
    position: relative;
    z-index: 2;
  }
  
  a {
    color: ${({ theme }) => theme.primary};
    text-decoration: none;
    transition: color 0.3s ease;
    
    &:hover {
      color: ${({ theme }) => theme.accent};
    }
  }
  
  button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
  }
  
  h1, h2, h3, h4, h5, h6 {
    margin-bottom: 1rem;
    font-weight: 700;
    line-height: 1.2;
  }
  
  img {
    max-width: 100%;
    height: auto;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: ${({ theme }) => theme.backgroundAlt};
  }
  
  ::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.accent};
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: ${({ theme }) => theme.primary};
  }
  
  /* 确保canvas元素正确显示 */
  canvas {
    display: block;
    outline: none;
  }

  /* 主题切换动画优化 */
  * {
    will-change: auto;
  }

  /* 为动画元素启用硬件加速 */
  .theme-animation-overlay {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* 减少动画期间的重绘 */
  .theme-transitioning {
    pointer-events: none;
  }

  /* 确保 GIF 动画流畅播放 */
  .gif-animation {
    image-rendering: auto;
    image-rendering: crisp-edges;
    image-rendering: -webkit-optimize-contrast;
  }
`;

export default GlobalStyle; 