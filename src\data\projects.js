const projects = [
  {
    id: 1,
    title: '交互式校园信息展示网站',
    description: '基于React+TypeScript+Three.js的沉浸式校园数字孪生网站，完全独立开发（从对接客户到部署上线），3D地球模型入口，信息查找效率提升80%，日均访问200+。',
    highlightTerms: ['React', 'TypeScript', 'Three.js', '100%', '3D', '200+', '10分钟', '2分钟', '80%', '响应式', '独立开发'],
    image: '/images/projects/Campus00.png',
    techStack: ['React', 'TypeScript', 'Three.js', 'Leaflet', 'Tailwind CSS', 'Vite', 'Framer Motion'],
    category: 'web',
    liveUrl: 'https://greenpulsemap.com/',
    highlights: [
      'Three.js 3D地球可视化：校园建筑三维建模，支持360°全景漫游',
      '智能导航系统：Leaflet引擎实现室内外路径规划，定位精度3米级',
      '动态交互体验：Framer Motion动画过渡，操作流畅度提升50%',
      '社区共创机制：用户照片上传生成热门打卡点热力图',
      '全终端响应式：移动端适配率100%，多设备体验一致'
    ],
    achievements: [
      '功能覆盖：3D校园漫游、交互式地图导航、多校区切换、社区照片分享',
      '项目成果：完全独立开发（对接客户->部署上线），Three.js 3D渲染优化，移动端适配率100%',
      '场景价值：重构校园信息获取方式，解决传统导览效率低、信息分散问题',
      '量化收益：日均访问200+，信息查找效率提升80%，用户满意度95%'
    ],
    galleryImages: [
      '/images/projects/Campus1.png',
      '/images/projects/Campus00.png',
      '/images/projects/Campus0.png',
      '/images/projects/Campus2.png',
      '/images/projects/Campus3.png',
      '/images/projects/campus4.png',
      '/images/projects/campus5.png'
    ]
  },
  {
    id: 2,
    title: '网球热赛事管理小程序',
    description: '基于微信小程序+Node.js+MongoDB的全链路网球赛事管理平台，实现Socket.io实时比分同步，管理效率提升87%，支持日均50+赛事。微信搜索"网球热"体验。',
    highlightTerms: ['微信小程序', 'Node.js', 'MongoDB', '100%', '5', '8', '30+', '90%', '50+', '2小时', '15分钟', '87%', 'Socket.io', '实时同步'],
    image: '/images/projects/tennis0.png',
    techStack: ['微信小程序', 'Node.js', 'Express.js', 'MongoDB', 'Socket.io', 'JWT', '微信支付API'],
    category: 'miniprogram',
    demoInfo: '微信搜索"网球热"',
    highlights: [
      'Socket.io毫秒级比分同步，观众与裁判端数据无缝衔接',
      '智能赛事推荐引擎，基于用户偏好标签匹配效率提升60%',
      '全流程自动化：报名-支付-签到-成绩公示零人工干预',
      '多端通知体系，关键节点微信模板消息触达率100%'
    ],
    achievements: [
      '功能覆盖赛事全生命周期：赛事发布、用户认证、实时比分、支付报名、社交分享',
      '项目成果：独立完成100%全栈开发，提前5天交付8个核心页面；30+用户测试UI满意度90%',
      '场景价值：重构网球赛事管理流程，解决传统模式效率低、信息不透明行业痛点',
      '量化收益：单场赛事管理时间从2小时缩短至15分钟，效率提升87%，支持日均50+赛事规模'
    ],
    galleryImages: [
      '/images/projects/tennis0.png',
      '/images/projects/tennis1.png',
      '/images/projects/tennis2.png',
      '/images/projects/tennis.png'
    ]
  },
  {
    id: 6,
    title: '生鲜商城小程序',
    description: '基于Uniapp+TS+Vue的生鲜购物小程序，独立完成100%前端开发，订单处理效率提升90%，支持日均200+订单。',
    highlightTerms: ['Uniapp', 'TS', 'Vue', '微信开发者工具', '100%', '7', '5', '50+', '85%', '200+', '5分钟', '30秒', '90%'],
    image: '/images/projects/veg0.png',
    techStack: ['Uniapp', 'TypeScript', 'Vue', '微信开发者工具'],
    category: 'miniprogram',
    highlights: [
      '合理分类生鲜，助顾客速找商品',
      '购物车支持查看商品、调整数量',
      '下单自动处理，流程便捷',
      '多终端适配'
    ],
    achievements: [
      '功能包括商品分类、购物车管理、订单处理、用户注册和配送时间选择',
      '项目成果：独立完成100%前端开发，提前7天完成5个核心页面；50+用户测试，UI满意度85%；深入理解Uniapp跨端开发与Vue组件通信',
      '·场景价值：为家庭生鲜配送定制，解决手写记账效率低、易出错问题',
      '预计日处理订单200+，单笔订单处理时间从5分钟缩短至30秒，效率提升90%'
    ],
    galleryImages: [
      '/images/projects/veg0.png',
      '/images/projects/veg1.png',
      '/images/projects/veg2.png',
      '/images/projects/veg3.png',
      '/images/projects/veg4.png',
      '/images/projects/veg5.png',
      '/images/projects/veg6.png',
      '/images/projects/veg7.png'
    ]
  },
  {
    id: 3,
    title: '学生积分管理小程序',
    description: '基于微信小程序+Node.js+MongoDB的K12家校协同管理平台，支持多媒体作业提交和智能积分体系，审核效率提升83%。',
    highlightTerms: ['微信小程序', 'Node.js', 'MongoDB', '100%', '8', '100+', '30分钟', '5分钟', '83%', '多媒体', '积分体系'],
    image: '/images/projects/stu1.png',
    techStack: ['微信小程序', 'Node.js', 'Express.js', 'MongoDB', 'JWT', 'Mongoose', '微信云存储'],
    category: 'miniprogram',
    highlights: [
      '4K多媒体作业提交：支持视频/图片/文字多格式批阅，还原线下教学体验',
      '动态积分激励系统：可视化成长曲线+徽章体系，学习参与度提升40%',
      '双角色权限架构：学生端提交追踪与家长端审核反馈精准隔离',
      '数据化成长档案：自动生成作业分析报告，支持导出存档'
    ],
    achievements: [
      '功能覆盖：用户认证、多媒体作业管理、智能积分统计、双角色权限控制',
      '项目成果：独立完成100%全栈开发，8个核心页面零BUG交付，支持双角色协同',
      '场景价值：解决传统作业管理效率低、家校沟通断层、激励机制缺失问题',
      '量化收益：日均处理作业100+，审核效率提升83%，学习主动性提升40%'
    ],
    galleryImages: [
      '/images/projects/stu_logo0.png',
      '/images/projects/stu_logo1.png',
      '/images/projects/stu0.jpg',
      '/images/projects/stu1.png',
      '/images/projects/stu2.png',
      '/images/projects/stu4.png'
    ]
  },
  {
    id: 4,
    title: '慧眸智检 — 智能行李安检系统',
    description: '基于Python+PyTorch+YOLOv8的智能行李安检系统，通过注意力模块优化算法，mAP50-95达79.7%，误报率降低50%。',
    highlightTerms: ['Python', 'PyTorch', 'YOLOv8', 'PIDray', 'mAP50-95', '79.7%', '50%'],
    image: '/images/projects/yolo4.png',
    techStack: ['Python', 'PyTorch', 'YOLOv8', '计算机视觉'],
    category: 'ai',
    highlights: [
      '引入空间/通道注意力模块优化算法',
      'mAP50-95提升至79.7%',
      '误报率降低50%',
      '前端界面优化交互体验'
    ],
    achievements: [
      'PIDray数据集上性能优异',
      '前端界面交互流畅',
      '优化行李安检识别精度',
      '降低安检误报率，提高效率'
    ],
    galleryImages: [
      '/images/projects/yolo0.png',
      '/images/projects/yolo1.png',
      '/images/projects/yolo2.png',
      '/images/projects/yolo3.png',
      '/images/projects/yolo4.png',
      '/images/projects/yolo5.png',
      '/images/projects/yolo6.png',
      '/images/projects/yolo7.png'
    ]
  },
  {
    id: 5,
    title: '猫爪印掌阅 - 智能体系统',
    description: '基于Python+Coze+RAG技术的智能体系统，构建30+作家人格模型，语言风格还原度85%，解析120部经典作品。',
    highlightTerms: ['Python', 'Coze', 'RAG技术', '30+', '85%', '120', '5', 'AI', '46%'],
    image: '/images/projects/Memo0.png',
    techStack: ['Python', 'Coze', 'RAG技术', 'AI'],
    category: 'ai',
    demoInfo: 'Coze搜索"猫爪印掌阅"',
    highlights: [
      '构建30+作家人格模型',
      '语言风格还原度85%',
      '5种视角解读',
      'AI金句配图生成'
    ],
    achievements: [
      '解析120部经典文学作品',
      '用户理解深度提升46%',
      '开发拟人化"作家猫咪"交互',
      '将AI技术与文学解析融合'
    ],
    galleryImages: [
      '/images/projects/Memo0.png',
      '/images/projects/Memo1.png',
      '/images/projects/Memo2.png',
      '/images/projects/Memo3.png',
      '/images/projects/Memo4.png',
      '/images/projects/Memo5.png',
      '/images/projects/Memo6.png'
    ]
  },
  {
    id: 13,
    title: '小红书用户画像智能体',
    description: '基于Coze+AI的小红书用户画像分析系统，通过URL链接批量提取内容，智能分析产品属性和用户评论，生成精准用户画像。',
    highlightTerms: ['Coze', 'AI', '批量提取', '词频统计', '用户画像', '智能分析'],
    image: '/images/projects/redbook0.png',
    techStack: ['Coze', 'AI', '自然语言处理', '数据分析'],
    category: 'ai',
    demoInfo: 'Coze搜索"小红书画像精灵"',
    highlights: [
      '批量URL内容提取：支持多个小红书链接同时分析',
      '智能关键词提取：自动识别产品属性、卖点、使用场景',
      '词频统计分析：精准统计关键词出现频率',
      'AI画像生成：基于数据分析生成详细用户画像'
    ],
    achievements: [
      '功能覆盖：URL批量处理、内容提取、关键词分析、画像生成',
      '技术亮点：多步骤智能处理流程，数据驱动的用户洞察',
      '应用价值：助力电商运营精准定位目标用户群体',
      '效率提升：自动化分析替代人工调研，节省80%时间成本'
    ],
    galleryImages: [
      '/images/projects/redbook0.png',
      '/images/projects/redbook1.png'
    ]
  },
  {
    id: 14,
    title: '发票记录智能体',
    description: '基于Coze+飞书API的智能发票管理系统，支持专票、普票、移动支付截图识别，自动写入飞书多维表格，财务处理效率提升90%。',
    highlightTerms: ['Coze', '飞书API', 'OCR识别', '90%', '自动化', '多维表格'],
    image: '/images/projects/fapiao0.jpg',
    techStack: ['Coze', '飞书API', 'OCR技术', '自动化'],
    category: 'ai',
    demoInfo: 'Coze搜索"发票精灵"',
    highlights: [
      'OCR智能识别：支持专票、普票、移动支付截图三种格式',
      '飞书表格集成：自动写入对应子表格，字段精准匹配',
      '错误处理机制：识别失败时详细反馈未写入位置',
      '成功确认反馈：写入完成后返回具体录入内容'
    ],
    achievements: [
      '功能覆盖：图像识别、数据提取、表格写入、异常处理',
      '技术成果：三类发票格式全覆盖，字段自动分类存储',
      '应用价值：解决财务录入效率低、易出错的传统痛点',
      '效率收益：发票处理时间缩短90%，准确率接近90%'
    ],
    galleryImages: [
      '/images/projects/fapiao0.jpg',
      '/images/projects/fapiao1.jpg',
      '/images/projects/fapiao2.jpg',
      '/images/projects/fapiao3.jpg',
      '/images/projects/fapiao4.png'
    ]
  },
  
  {
    id: 7,
    title: '智能井盖监测系统',
    description: '基于物联网技术的智能井盖监测系统，采用4G/5G/NB-IoT/LoRa通信，实现环境参数监测和井盖状态异常报警。',
    highlightTerms: ['物联网', '4G/5G', 'NB-IoT', 'LoRa', '低功耗', '蓝牙', 'APP'],
    image: '/images/projects/cover0.png',
    techStack: ['物联网', '传感器技术', '无线通信', '低功耗设计', '移动APP'],
    category: 'embedded',
    highlights: [
      '监测井下水位、温湿度、气体浓度等多种环境参数',
      '无线连接使用4G/5G/NB-IoT/LoRa等多种网络',
      '井盖状态监测及数据异常报警功能',
      '低功耗设计与参数可配置'
    ],
    achievements: [
      '实现井盖非法开启/松动/掉落/丢失等状态监测',
      '支持设备布防状态、电量、信号强度等定时上报',
      '采用低功耗设计延长电池寿命',
      '支持蓝牙连接，通过手机APP设置参数'
    ],
    galleryImages: [
      '/images/projects/cover0.png',
      '/images/projects/cover1.png',
      '/images/projects/cover2.jpg',
      '/images/projects/cover3.jpg',
      '/images/projects/cover4.png',
      '/images/projects/cover5.png'
    ]
  },
  {
    id: 8,
    title: '智能门锁与环境检测系统',
    description: '基于ESP32单片机的智能门锁与环境监测系统，使用C语言实现数据采集、显示、报警和人机交互功能。',
    highlightTerms: ['ESP32', 'STM32', 'LED', 'OLED', 'C语言'],
    image: '/images/projects/esp32-3.png',
    techStack: ['ESP32', 'C语言', '嵌入式开发', '传感器技术'],
    category: 'embedded',
    highlights: [
      '设计电路集成多种传感器',
      '实现数据采集与显示功能',
      '开发报警与人机交互界面',
      '系统模块化设计，易于维护'
    ],
    achievements: [
      '完整实现环境监测功能',
      '代码结构清晰，模块化程度高',
      '系统稳定性和可靠性强',
      '便于后期维护和功能扩展'
    ],
    galleryImages: [
      '/images/projects/esp32-0.png',
      '/images/projects/esp32-1.png',
      '/images/projects/esp32-2.png',
      '/images/projects/esp32-3.png',
      '/images/projects/esp32-4.png',
      '/images/projects/esp32-5.png'
    ]
  },
  {
    id: 9,
    title: '天气查询网页',
    description: '基于React+TypeScript的天气查询应用，面向2000+学生群体，解决传统App广告多、定位不准的痛点。',
    highlightTerms: ['React', 'TypeScript', '2000+'],
    image: '/images/projects/weather0.png',
    techStack: ['React', 'TypeScript', 'Axios', 'OpenWeatherMap API'],
    category: 'web',
    highlights: [
      '响应式设计适配多种设备',
      'API数据缓存优化加载速度',
      '自动定位功能'
    ],
    achievements: [
      '面向2000+学生群体的本地化天气服务',
      '解决传统App广告多、定位不准痛点',
      '深入理解React组件化开发',
      '代码作为学习笔记分享'
    ],
    galleryImages: [
      '/images/projects/weather0.png',
      '/images/projects/weather1.png',
      '/images/projects/weather3.gif'
    ]
  },
  {
    id: 10,
    title: 'React TodoList 应用',
    description: '基于React+TypeScript+Redux的任务管理应用，支持任务增删改查和状态管理，2周完成开发。',
    highlightTerms: ['React', 'TypeScript', 'Redux', '2周'],
    image: '/images/projects/Todolist0.png',
    techStack: ['React', 'TypeScript', 'Redux', 'CSS Modules'],
    category: 'web',
    highlights: [
      '组件化开发提高代码复用性',
      'TypeScript强类型确保代码质量',
      'Redux状态管理保持数据一致性'
    ],
    achievements: [
      '2周完成初版开发',
      '掌握Redux状态管理',
      '作为TypeScript学习实践项目'
    ],
    galleryImages: [
      '/images/projects/Todolist0.png',
      '/images/projects/Todolist1.png',
      '/images/projects/Todolist2.png'
    ]
  },
  {
    id: 11,
    title: '拟物3D计算器',
    description: '基于React+styled-components的拟物化3D计算器，通过CSS3实现独特3D按键效果，课程作业获A评级。',
    highlightTerms: ['React', 'styled-components', '3D', 'CSS3', 'A评级'],
    image: '/images/projects/Calculator2.png',
    techStack: ['React', 'styled-components', 'JavaScript'],
    category: 'web',
    highlights: [
      '使用styled-components实现独特3D按键效果',
      '提升交互体验',
      '响应式设计适配移动端'
    ],
    achievements: [
      '独立完成所有UI组件设计',
      '课程作业获A评级',
      '优化按键交互体验'
    ],
    galleryImages: [
      '/images/projects/Calculator2.png',
      '/images/projects/Calculator1_2024-12-04_11-13.gif',
      '/images/projects/Calculator2_2024-12-04_11-37.gif'
    ]
  },
  {
    id: 12,
    title: '飞机大战游戏',
    description: '基于HTML5 Canvas+JavaScript的飞机射击游戏，实现3种敌机类型，代码复用率70%，10名同学参与测试。',
    highlightTerms: ['HTML5 Canvas', 'JavaScript', 'Canvas', '3种', '70%', '10名'],
    image: '/images/projects/Plane8.png',
    techStack: ['HTML5 Canvas', 'JavaScript', 'CSS3'],
    category: 'web',
    highlights: [
      'Canvas动画效果流畅',
      '游戏逻辑与渲染分离',
      '响应式控制系统'
    ],
    achievements: [
      '实现3种敌机类型',
      '代码复用率70%',
      '10名同学参与测试',
      '深入理解Canvas动画原理'
    ],
    galleryImages: [
      '/images/projects/Plane6.png',
      '/images/projects/Plane7.png',
      '/images/projects/Plane8.png'
    ]
  },
  {
    id: 15,
    title: '海报封面LOGO设计',
    description: '个人兴趣驱动的视觉设计作品集，涵盖海报设计、品牌LOGO、应用图标等多种类型，展现创意思维和美学素养。',
    highlightTerms: ['视觉设计', 'LOGO', '海报', '品牌', '创意'],
    image: '/images/projects/stu_logo0.png',
    techStack: ['可画', 'AI生图', 'ChatGPT', '豆包', 'Napkin'],
    category: 'design',
    highlights: [
      '多元化设计风格：从简约现代到复古经典',
      '品牌视觉统一：LOGO与应用场景完美融合',
      '创意海报设计：视觉冲击力与信息传达并重',
      '实用性考量：设计作品具备实际应用价值'
    ],
    achievements: [
      '设计类型：海报设计、LOGO创作、应用图标、品牌视觉',
      '创作理念：将技术思维融入视觉表达，理性与感性结合',
      '应用场景：个人项目品牌化，提升产品视觉识别度',
      '设计价值：培养审美能力，为产品开发增添设计思维'
    ],
    galleryImages: [
      '/images/projects/join the fight!.png',
      '/images/projects/stu_logo0.png',
      '/images/projects/stu_logo1.png',
      '/images/projects/stu6.jpg',
      '/images/projects/Yolo_V1.pptx.png'
    ]
  }
];

export default projects; 


