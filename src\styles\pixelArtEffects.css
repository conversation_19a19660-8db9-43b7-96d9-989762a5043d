/* 像素艺术风格效果 */

/* 像素化滤镜 - 应用于整个地图容器 */
.pixel-art-container {
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
}

/* 复古色彩滤镜 - 柔和低饱和度的颜色 */
.retro-filter {
  filter: saturate(0.85) contrast(1.1) brightness(0.95);
}

/* 像素边框效果 */
.pixel-border {
  box-shadow: 
    0 4px 0 0 rgba(0, 0, 0, 0.2),
    inset 0 0 0 2px rgba(255, 255, 255, 0.1);
  image-rendering: pixelated;
}

/* 像素风格UI元素 */
.pixel-ui {
  font-family: 'Press Start 2P', cursive;
  padding: 10px;
  border: 4px solid;
  border-image-slice: 2;
  border-image-width: 2;
  border-image-repeat: stretch;
  border-image-source: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12"><path d="M2 2h8v8H2z" fill="none" stroke="white" stroke-width="2"/></svg>');
  box-shadow: 0 4px 0 rgba(0, 0, 0, 0.3);
  image-rendering: pixelated;
}

/* 像素风格按钮 */
.pixel-button {
  background-color: #7e7e7e;
  color: white;
  border: 0;
  padding: 8px 12px;
  font-family: 'Press Start 2P', cursive;
  font-size: 12px;
  position: relative;
  box-shadow: 
    0 4px 0 #383838,
    0 0 0 2px #000000;
  transition: all 0.1s;
  image-rendering: pixelated;
}

.pixel-button:active {
  top: 4px;
  box-shadow: 
    0 0 0 #383838,
    0 0 0 2px #000000;
}

/* 像素风格阴影效果 */
.pixel-shadow {
  box-shadow: 
    4px 0 0 0 rgba(0, 0, 0, 0.2),
    0 4px 0 0 rgba(0, 0, 0, 0.2),
    4px 4px 0 0 rgba(0, 0, 0, 0.2);
  image-rendering: pixelated;
}

/* 像素风格光照效果 */
.pixel-light {
  position: relative;
}

.pixel-light::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

/* 像素风格水体效果 */
.pixel-water {
  position: relative;
  overflow: hidden;
}

.pixel-water::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.1) 2px,
    transparent 2px,
    transparent 4px
  );
  animation: waterAnimation 2s infinite linear;
  pointer-events: none;
}

@keyframes waterAnimation {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(4px);
  }
}

/* 像素风格草地效果 */
.pixel-grass {
  position: relative;
}

.pixel-grass::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(
      circle at 30% 30%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px,
      transparent 100%
    ),
    radial-gradient(
      circle at 70% 60%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px,
      transparent 100%
    );
  background-size: 8px 8px;
  pointer-events: none;
}

/* 像素风格山脉效果 */
.pixel-mountain {
  position: relative;
}

.pixel-mountain::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

/* 像素风格建筑效果 */
.pixel-building {
  position: relative;
}

.pixel-building::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    to right,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.1) 2px,
    transparent 2px,
    transparent 8px
  );
  pointer-events: none;
}

/* 像素风格森林效果 */
.pixel-forest {
  position: relative;
}

.pixel-forest::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(
      circle at 30% 30%,
      rgba(0, 0, 0, 0.1) 0%,
      rgba(0, 0, 0, 0.1) 2px,
      transparent 2px,
      transparent 100%
    ),
    radial-gradient(
      circle at 70% 60%,
      rgba(0, 0, 0, 0.1) 0%,
      rgba(0, 0, 0, 0.1) 2px,
      transparent 2px,
      transparent 100%
    );
  background-size: 10px 10px;
  pointer-events: none;
}

/* 像素风格光晕效果 */
.pixel-glow {
  animation: pixelGlow 2s infinite alternate;
}

@keyframes pixelGlow {
  0% {
    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.7));
  }
  100% {
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.9));
  }
}

/* 像素风格猫咪效果 */
.pixel-cat {
  position: relative;
}

.pixel-cat::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

/* 像素风格宝藏效果 */
.pixel-treasure {
  animation: treasureGlow 1.5s infinite alternate;
}

@keyframes treasureGlow {
  0% {
    filter: brightness(1) drop-shadow(0 0 2px rgba(255, 183, 3, 0.7));
  }
  100% {
    filter: brightness(1.2) drop-shadow(0 0 5px rgba(255, 183, 3, 0.9));
  }
}

/* 像素风格记忆碎片效果 */
.pixel-memory {
  animation: memoryPulse 2s infinite alternate;
}

@keyframes memoryPulse {
  0% {
    filter: hue-rotate(0deg) brightness(1);
  }
  100% {
    filter: hue-rotate(30deg) brightness(1.2);
  }
} 