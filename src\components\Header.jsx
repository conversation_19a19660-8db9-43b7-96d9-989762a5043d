import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';

const HeaderContainer = styled.header`
  background-color: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1rem 2rem;
  position: sticky;
  top: 0;
  z-index: 100;
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
`;

const Nav = styled.nav`
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
`;

const Logo = styled(Link)`
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  text-decoration: none;
  transition: color 0.3s ease;
  
  &:hover {
    color: #6aa0f0;
  }
`;

const NavLinks = styled.div`
  display: flex;
  align-items: center;
  gap: 2rem;
  
  @media (max-width: 768px) {
    display: ${({ $isOpen }) => ($isOpen ? 'flex' : 'none')};
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #ffffff;
    padding: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    gap: 1rem;
  }
`;

const NavLink = styled(Link)`
  color: ${({ $active }) => ($active ? '#6aa0f0' : '#333')};
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0;
  position: relative;
  transition: color 0.3s ease;
  
  &:hover {
    color: #6aa0f0;
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #6aa0f0;
    transform: scaleX(${({ $active }) => ($active ? 1 : 0)});
    transform-origin: bottom left;
    transition: transform 0.3s ease;
  }
  
  &:hover::after {
    transform: scaleX(1);
  }
`;

const AdventureLink = styled(NavLink)`
  background: linear-gradient(135deg, #6aa0f0, #a4c7f9);
  color: white !important;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(106, 160, 240, 0.3);
  }
  
  &::after {
    display: none;
  }
`;

const MobileMenuButton = styled.button`
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #333;
  
  @media (max-width: 768px) {
    display: block;
  }
`;

const Header = () => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();
  
  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };
  
  return (
    <HeaderContainer>
      <Nav>
        <Logo to="/">Tully</Logo>
        <MobileMenuButton onClick={toggleMenu}>
          {isOpen ? '✕' : '☰'}
        </MobileMenuButton>
        <NavLinks $isOpen={isOpen}>
          <NavLink to="/projects" $active={location.pathname === '/projects'}>
            项目集
          </NavLink>
          <NavLink to="/skills" $active={location.pathname === '/skills'}>
            技术栈
          </NavLink>
          <NavLink to="/about" $active={location.pathname === '/about'}>
            关于我
          </NavLink>
          <NavLink to="/interests" $active={location.pathname === '/interests'}>
            兴趣爱好
          </NavLink>
          <AdventureLink to="/adventure-map" $active={location.pathname === '/adventure-map'}>
            探险地图
          </AdventureLink>
        </NavLinks>
      </Nav>
    </HeaderContainer>
  );
};

export default Header; 