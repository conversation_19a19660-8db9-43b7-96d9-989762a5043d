import React from 'react';
import styled, { keyframes } from 'styled-components';
import { motion } from 'framer-motion';

// 动画
const rotate = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;

const pulse = keyframes`
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
`;

// 样式组件
const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  background-color: ${({ theme }) => theme.background};
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
`;

const LoadingSpinner = styled.div`
  width: 80px;
  height: 80px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: ${({ theme }) => theme.primary};
  border-radius: 50%;
  animation: ${rotate} 1s linear infinite;
  margin-bottom: 20px;
`;

const LoadingText = styled(motion.div)`
  font-size: 1.5rem;
  font-weight: 700;
  color: ${({ theme }) => theme.text};
  letter-spacing: 2px;
  animation: ${pulse} 1.5s ease-in-out infinite;
`;

const ProgressBar = styled.div`
  width: 200px;
  height: 5px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  margin-top: 20px;
  overflow: hidden;
  position: relative;
`;

const ProgressFill = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: ${props => props.progress}%;
  background: linear-gradient(
    to right,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  border-radius: 10px;
  transition: width 0.3s ease;
`;

// 加载提示语
const loadingTexts = [
  "正在加载3D元素...",
  "准备视觉效果...",
  "初始化粒子系统...",
  "构建星际空间...",
  "调整光照效果..."
];

const Loading = ({ progress = 0 }) => {
  const [loadingTextIndex, setLoadingTextIndex] = React.useState(0);
  
  // 循环切换加载提示
  React.useEffect(() => {
    const interval = setInterval(() => {
      setLoadingTextIndex((prev) => (prev + 1) % loadingTexts.length);
    }, 2000);
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <LoadingContainer>
      <LoadingSpinner />
      <LoadingText
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {loadingTexts[loadingTextIndex]}
      </LoadingText>
      <ProgressBar>
        <ProgressFill progress={progress || Math.random() * 100} />
      </ProgressBar>
    </LoadingContainer>
  );
};

export default Loading; 