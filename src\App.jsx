import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { AnimatePresence } from 'framer-motion';

// 主题
import { sunTheme, moonTheme, starTheme } from './themes';
import GlobalStyle from './globalStyles';
import { ThemeContext } from './context/ThemeContext';

// 组件
import Navigation from './components/Navigation';
import Footer from './components/Footer';

// 页面
import Home from './pages/Home';
import Projects from './pages/Projects';
import Skills from './pages/Skills';
import About from './pages/About';
import Contact from './pages/Contact';
import ProjectDetail from './pages/ProjectDetail';

// 滚动组件
import ScrollIndicator from './components/ScrollIndicator';

// 主页面组件
const MainPage = () => {
  const [activeSection, setActiveSection] = useState('home');
  
  // 监听滚动事件，更新当前活动的部分
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const sections = document.querySelectorAll('section[id]');
      
      sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');
        
        if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
          setActiveSection(sectionId);
        }
      });
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);
  
  // 滚动到指定部分
  const scrollToSection = (sectionId) => {
    const section = document.getElementById(sectionId);
    if (section) {
      window.scrollTo({
        top: section.offsetTop - 80,
        behavior: 'smooth'
      });
    }
  };

  return (
    <>
      <Navigation activeSection={activeSection} onNavClick={scrollToSection} />
      <main>
        <Home id="home" />
        <Projects id="projects" />
        <Skills id="skills" />
        <About id="about" />
        <Contact id="contact" />
      </main>
      <ScrollIndicator 
        sections={['home', 'projects', 'skills', 'about', 'contact']} 
        activeSection={activeSection}
        onDotClick={scrollToSection}
      />
      <Footer />
    </>
  );
};

function App() {
  const [theme, setTheme] = React.useState('star');
  
  // 切换主题 (三种模式循环)
  const toggleTheme = () => {
    const themeOrder = ['sun', 'moon', 'star'];
    const currentIndex = themeOrder.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themeOrder.length;
    const nextTheme = themeOrder[nextIndex];
    
    setTheme(nextTheme);
    localStorage.setItem('theme', nextTheme);
  };
  
  // 从本地存储中获取主题设置，默认为星星主题
  React.useEffect(() => {
    const savedTheme = localStorage.getItem('theme');
    // 设置为星星主题作为默认
    setTheme(savedTheme || 'star');
    if (!savedTheme) {
      localStorage.setItem('theme', 'star');
    }
  }, []);
  
  return (
    <Router>
      <ThemeContext.Provider value={{ theme, toggleTheme }}>
        <ThemeProvider theme={
          theme === 'sun' ? sunTheme : 
          theme === 'moon' ? moonTheme : 
          starTheme
        }>
          <GlobalStyle />
          
          <Routes>
            <Route path="/" element={<MainPage />} />
            <Route path="/project/:id" element={<ProjectDetail />} />
          </Routes>
          
        </ThemeProvider>
      </ThemeContext.Provider>
    </Router>
  );
}

export default App;