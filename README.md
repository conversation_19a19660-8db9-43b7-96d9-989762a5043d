# Tully的个人作品集网站

这是一个基于React和styled-components开发的个人作品集网站，主要展示我的项目经验、技术栈和兴趣爱好。

## 技术栈

- React 18
- React Router v6
- styled-components
- Framer Motion
- Vite

## 项目结构

```
personal-blog/
│
├── public/             # 静态资源
├── src/                # 源代码
│   ├── components/     # 可复用组件
│   ├── pages/          # 页面组件
│   ├── data/           # 数据文件
│   ├── styles/         # 样式文件
│   ├── App.jsx         # 应用主组件
│   └── index.jsx       # 入口文件
│
├── index.html          # HTML模板
├── vite.config.js      # Vite配置
└── package.json        # 项目依赖
```

## 功能特性

- 响应式设计，适配各种设备
- 平滑过渡动画
- 作品集展示
- 技术栈可视化
- 个人简介
- 兴趣爱好展示

## 本地开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 部署

本项目使用Vite构建工具，可以轻松部署到任何静态网站托管服务，如Netlify、Vercel、GitHub Pages等。

## 联系方式

- 邮箱：<EMAIL>
- GitHub：https://github.com/Tully-L 
