<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Tully - tully">
  <meta name="keywords" content="tully,<PERSON><PERSON>,前端开发,<PERSON><PERSON>,<PERSON><PERSON>,作品集,个人网站">
  <meta name="author" content="Tully">
  <link rel="icon" href="/Memoo.ico">
  <title>Tully</title>
  <!-- 预加载字体 -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <!-- 引入Google字体 -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Press+Start+2P&display=swap" rel="stylesheet">
  <!-- 引入Font Awesome图标库 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <!-- 引入微软雅黑字体 -->
  <style>
    @font-face {
      font-family: "微软雅黑";
      src: local("Microsoft YaHei");
      font-display: swap;
    }
  </style>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/index.jsx"></script>
</body>
</html> 