import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import skills from '../data/skills';
import PageSection from '../components/PageSection';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  color: ${({ theme }) => theme.text};
  margin-bottom: 1rem;
  background: linear-gradient(
    to right,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
`;

const Description = styled.p`
  font-size: 1.1rem;
  color: ${({ theme }) => theme.textSecondary};
  max-width: 700px;
  margin: 0 auto;
`;

const SkillsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 3rem;
`;

const CategorySection = styled(motion.div)`
  margin-bottom: 2rem;
`;

const CategoryTitle = styled.h2`
  font-size: 1.8rem;
  color: ${({ theme }) => theme.text};
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid ${({ theme }) => theme.borderLight};
`;

const SkillsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const SkillCard = styled(motion.div)`
  background-color: ${({ theme }) => theme.cardBg};
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  padding: 1.5rem;
`;

const SkillHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
`;

const SkillIcon = styled.div`
  width: 48px;
  height: 48px;
  background-color: ${({ theme }) => theme.backgroundAlt};
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.accent};
  font-size: 1.5rem;
  margin-right: 1rem;
`;

const SkillName = styled.h3`
  font-size: 1.3rem;
  color: ${({ theme }) => theme.text};
  margin: 0;
`;

const SkillProgressContainer = styled.div`
  margin-bottom: 1rem;
`;

const SkillProgressBar = styled.div`
  height: 8px;
  background-color: ${({ theme }) => theme.backgroundAlt};
  border-radius: 4px;
  overflow: hidden;
`;

const SkillProgressFill = styled.div`
  height: 100%;
  width: ${props => props.level}%;
  background: linear-gradient(
    to right,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  border-radius: 4px;
`;

const SkillDescription = styled.p`
  font-size: 0.95rem;
  color: ${({ theme }) => theme.textSecondary};
  line-height: 1.5;
`;

const Skills = () => {
  // 图标映射
  const getIconContent = (icon) => {
    const iconMap = {
      react: '⚛️',
      vue: '🌟',
      typescript: '📘',
      css: '🎨',
      html: '📄',
      uniapp: '📱',
      nodejs: '🟢',
      express: '🚂',
      flask: '🧪',
      mysql: '🗄️',
      wechat: '💬',
      embedded: '🔌',
      git: '🔄',
      design: '🎭',
    };
    
    return iconMap[icon] || '💻';
  };
  
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const cardVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <PageSection id="skills">
      <Container>
        <Header>
          <Title>技术栈</Title>
          <Description>
            以下是我掌握的技术栈，涵盖前端开发、开发框架和其他专业技能。我不断学习新技术，拓展自己的技术广度和深度。
          </Description>
        </Header>
        
        <SkillsContainer
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {skills.map((category, index) => (
            <CategorySection key={index}>
              <CategoryTitle>{category.category}</CategoryTitle>
              <SkillsGrid>
                {category.items.map((skill, skillIndex) => (
                  <SkillCard
                    key={skillIndex}
                    variants={cardVariants}
                    whileHover={{ y: -5, boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)' }}
                  >
                    <SkillHeader>
                      <SkillIcon>{getIconContent(skill.icon)}</SkillIcon>
                      <SkillName>{skill.name}</SkillName>
                    </SkillHeader>
                    <SkillProgressContainer>
                      <SkillProgressBar>
                        <SkillProgressFill level={skill.level} />
                      </SkillProgressBar>
                    </SkillProgressContainer>
                    <SkillDescription>{skill.description}</SkillDescription>
                  </SkillCard>
                ))}
              </SkillsGrid>
            </CategorySection>
          ))}
        </SkillsContainer>
      </Container>
    </PageSection>
  );
};

export default Skills; 