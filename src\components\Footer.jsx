import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import CircularText from './CircularText';

const FooterContainer = styled.footer`
  background-color: ${({ theme }) => theme.backgroundAlt};
  padding: 3rem 0;
  border-top: 1px solid ${({ theme }) => theme.border};
  position: relative;
  z-index: 1000;
`;

const FooterContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  
  @media (max-width: 768px) {
    padding: 0 1rem;
    grid-template-columns: 1fr;
  }
`;

const FooterSection = styled.div`
  h3 {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -0.5rem;
      left: 0;
      width: 50px;
      height: 2px;
      background: linear-gradient(
        to right,
        ${({ theme }) => theme.gradientStart},
        ${({ theme }) => theme.gradientEnd}
      );
    }
  }
  
  p {
    color: ${({ theme }) => theme.textSecondary};
    line-height: 1.6;
  }
`;

const SocialLinks = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
`;

const SocialIcon = styled(motion.a)`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.backgroundAlt};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.text};
  font-size: 1.2rem;
  transition: all 0.3s ease;
  border: 1px solid ${({ theme }) => theme.border};
  
  &:hover {
    transform: translateY(-5px);
    background-color: ${({ theme }) => theme.primary};
    color: white;
  }
`;

const FooterLinks = styled.ul`
  list-style: none;
  padding: 0;
  
  li {
    margin-bottom: 0.8rem;
    
    a {
      color: ${({ theme }) => theme.textSecondary};
      transition: color 0.3s ease;
      
      &:hover {
        color: ${({ theme }) => theme.primary};
      }
    }
  }
`;

const Copyright = styled.div`
  text-align: center;
  padding-top: 2rem;
  margin-top: 2rem;
  border-top: 1px solid ${({ theme }) => theme.border};
  color: ${({ theme }) => theme.textSecondary};
  font-size: 0.9rem;
  max-width: 1200px;
  margin: 2rem auto 0;
  padding: 2rem 2rem 0;
  
  a {
    color: ${({ theme }) => theme.primary};
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const ScrollToTopWrapper = styled(motion.div)`
  position: fixed;
  bottom: 0.8rem;
  right: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  width: 50px;
  height: 50px;
  cursor: pointer;
  
  @media (max-width: 768px) {
    width: 40px;
    height: 40px;
    bottom: 0.6rem;
    right: 0.6rem;
  }
`;

const CircularTextWrapper = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const CenterButton = styled(motion.div)`
  position: relative;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: linear-gradient(
    to right,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  z-index: 10;
  transition: transform 0.3s ease;
  
  @media (max-width: 768px) {
    width: 25px;
    height: 25px;
    font-size: 0.8rem;
  }
`;

const Footer = () => {
  const [showScrollTop, setShowScrollTop] = useState(false);

  // 监听滚动事件
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setShowScrollTop(scrollPosition > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 滚动到顶部函数
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <>
      <FooterContainer>
        <FooterContent>
          <FooterSection>
            <h3>关于我</h3>
            <p>我是Tully，一名充满创造力的前端开发工程师，专注于创建美观且交互性强的网站和应用程序。</p>
            <SocialLinks>
              <SocialIcon 
                href="https://github.com/Tully-L" 
                target="_blank" 
                rel="noopener noreferrer"
                animate={{ scale: 1 }}
                whileHover={{ y: -5, backgroundColor: '#4cc9f0', color: '#fff' }}
              >
                <i className="fab fa-github"></i>
              </SocialIcon>
              <SocialIcon 
                href="https://linkedin.com/" 
                target="_blank" 
                rel="noopener noreferrer"
                animate={{ scale: 1 }}
                whileHover={{ y: -5, backgroundColor: '#4cc9f0', color: '#fff' }}
              >
                <i className="fab fa-linkedin-in"></i>
              </SocialIcon>
              <SocialIcon 
                href="https://twitter.com/" 
                target="_blank" 
                rel="noopener noreferrer"
                animate={{ scale: 1 }}
                whileHover={{ y: -5, backgroundColor: '#4cc9f0', color: '#fff' }}
              >
                <i className="fab fa-twitter"></i>
              </SocialIcon>
            </SocialLinks>
          </FooterSection>
          
          <FooterSection>
            <h3>快速链接</h3>
            <FooterLinks>
              <li><a href="/">首页</a></li>
              <li><a href="/projects">项目</a></li>
              <li><a href="/skills">技能</a></li>
              <li><a href="/about">关于</a></li>
              <li><a href="/contact">联系</a></li>
            </FooterLinks>
          </FooterSection>
          
          <FooterSection>
            <h3>联系方式</h3>
            <FooterLinks>
              <li>邮箱：<EMAIL></li>
              <li>微信：Tully</li>
              <li>地点：中国</li>
            </FooterLinks>
          </FooterSection>
        </FooterContent>
        
        <Copyright>
          © {new Date().getFullYear()} Tully | <a href="https://github.com/Tully-L" target="_blank" rel="noopener noreferrer">GitHub</a>
        </Copyright>
      </FooterContainer>

      {/* 修改后的回到顶部按钮 */}
      <AnimatePresence>
        {showScrollTop && (
          <ScrollToTopWrapper
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
            transition={{ duration: 0.3 }}
            onClick={scrollToTop}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
            <CircularTextWrapper>
              <CircularText
                text="SCROLL·TO·TOP·"
                spinDuration={15}
                onHover="speedUp"
                size="60px"
                fontSize="8px"
              />
            </CircularTextWrapper>
            
            <CenterButton>
              <i className="fas fa-arrow-up"></i>
            </CenterButton>
          </ScrollToTopWrapper>
        )}
      </AnimatePresence>
    </>
  );
};

export default Footer; 