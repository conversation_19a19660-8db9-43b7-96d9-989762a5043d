import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';

// 进度指示器容器
const IndicatorContainer = styled.div`
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  height: 200px;
  width: 3px;
  background-color: ${({ theme }) => theme.backgroundAlt};
  border-radius: 3px;
  z-index: 100;
  overflow: hidden;
`;

// 进度条
const ProgressIndicator = styled(motion.div)`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(
    to bottom,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  border-radius: 3px;
`;

// 标记点容器
const MarkersContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 5px 0;
`;

// 标记点
const Marker = styled.div`
  width: 7px;
  height: 3px;
  background-color: ${({ $active, theme }) => 
    $active ? 'white' : 'transparent'};
  border-radius: 3px;
  transform: translateX(-2px);
  transition: all 0.3s ease;
`;

const ScrollIndicator = ({ sections, activeSection, onDotClick }) => {
  // 计算当前进度百分比
  const calculateProgress = () => {
    const index = sections.indexOf(activeSection);
    if (index !== -1) {
      return ((index) / (sections.length - 1)) * 100;
    }
    return 0;
  };

  return (
    <IndicatorContainer>
      <ProgressIndicator 
        initial={{ height: '0%' }}
        animate={{ height: `${calculateProgress()}%` }}
        transition={{ duration: 0.5 }}
      />
      <MarkersContainer>
        {sections.map((section, index) => (
          <Marker 
            key={section}
            $active={sections.indexOf(activeSection) >= index}
            onClick={() => onDotClick(section)}
            style={{ cursor: 'pointer' }}
          />
        ))}
      </MarkersContainer>
    </IndicatorContainer>
  );
};

export default ScrollIndicator; 