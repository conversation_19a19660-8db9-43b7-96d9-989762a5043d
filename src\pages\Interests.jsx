import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import interests from '../data/interests';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 1rem;
`;

const Description = styled.p`
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  font-size: 1.1rem;
  color: #666;
  max-width: 700px;
  margin: 0 auto;
`;

const InterestsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const InterestCard = styled(motion.div)`
  background-color: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  padding: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const InterestHeader = styled.div`
  padding: 1.5rem;
  background-color: #f0f7ff;
  display: flex;
  align-items: center;
`;

const InterestIcon = styled.div`
  font-size: 2.5rem;
  margin-right: 1rem;
`;

const InterestTitle = styled.h3`
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  font-size: 1.5rem;
  color: #333;
  margin: 0;
`;

const InterestContent = styled.div`
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
`;

const InterestDescription = styled.p`
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
`;

const InterestItems = styled.div`
  margin-top: auto;
`;

const InterestItem = styled.div`
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
`;

const InterestItemName = styled.h4`
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  font-size: 1.1rem;
  color: #333;
  margin: 0 0 0.5rem 0;
`;

const InterestItemDescription = styled.p`
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  font-size: 0.9rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
`;

const Interests = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const cardVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <Container>
      <Header>
        <Title>兴趣爱好</Title>
        <Description>
          工作之余，我热衷于各种兴趣爱好，这些活动不仅丰富了我的生活，也塑造了我的性格和思维方式。
        </Description>
      </Header>
      
      <InterestsGrid
        as={motion.div}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {interests.map((interest) => (
          <InterestCard
            key={interest.id}
            variants={cardVariants}
            whileHover={{ 
              y: -5,
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
            }}
          >
            <InterestHeader>
              <InterestIcon>{interest.icon}</InterestIcon>
              <InterestTitle>{interest.title}</InterestTitle>
            </InterestHeader>
            <InterestContent>
              <InterestDescription>{interest.description}</InterestDescription>
              <InterestItems>
                {interest.items.map((item, index) => (
                  <InterestItem key={index}>
                    <InterestItemName>{item.name}</InterestItemName>
                    <InterestItemDescription>{item.description}</InterestItemDescription>
                  </InterestItem>
                ))}
              </InterestItems>
            </InterestContent>
          </InterestCard>
        ))}
      </InterestsGrid>
    </Container>
  );
};

export default Interests; 