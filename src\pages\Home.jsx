import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import DecryptedText from '../components/DecryptedText';
import CountUp from '../components/CountUp';
import FeaturedProjects from '../components/FeaturedProjects';

// 组件
import AsciiText from '../components/ui/AsciiText';
import PageSection from '../components/PageSection';

const HeroContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  z-index: 10;
  position: relative;
  
  @media (max-width: 768px) {
    padding: 0 1rem;
  }
`;

const HeroSection = styled.section`
  min-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
`;

const HeroTitle = styled.h1`
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  font-weight: 800;
  line-height: 1.2;
  background: linear-gradient(
    to right,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  
  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const Subtitle = styled.div`
  font-size: 1.5rem;
  max-width: 800px;
  margin: 0 auto 2.5rem;
  color: ${({ theme }) => theme.textSecondary};
  line-height: 1.6;
  
  @media (max-width: 768px) {
    font-size: 1.2rem;
  }
  
  .highlighted {
    color: ${({ theme }) => theme.primary};
    font-weight: 600;
  }
  
  .encrypted {
    opacity: 0.8;
  }
`;

const ButtonContainer = styled(motion.div)`
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
  }
`;

const PrimaryButton = styled(motion.a)`
  padding: 0.8rem 2rem;
  background: linear-gradient(
    to right,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  color: white;
  border-radius: 30px;
  font-weight: 600;
  display: inline-block;
  cursor: pointer;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  }
`;

const SecondaryButton = styled(motion.a)`
  padding: 0.8rem 2rem;
  background: transparent;
  color: white;
  border: 2px solid ${({ theme }) => theme.primary};
  border-radius: 30px;
  font-weight: 600;
  display: inline-block;
  cursor: pointer;
  text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3);
  
  &:hover {
    background-color: ${({ theme }) => theme.backgroundAlt};
    transform: translateY(-3px);
    color: ${({ theme }) => theme.primary};
    text-shadow: none;
  }
`;

const ScrollIndicator = styled(motion.div)`
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: ${({ theme }) => theme.textSecondary};
  
  span {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
  }
  
  i {
    font-size: 1.5rem;
  }
`;

const Home = () => {
  const [currentRole, setCurrentRole] = useState("技术开发者");
  
  // 定期切换角色文本
  useEffect(() => {
    const roles = [
      "技术开发者",
      "创意实践者",
      "解决方案设计师",
      "数字创新者"
    ];
    
    let currentIndex = 0;
    
    const intervalId = setInterval(() => {
      currentIndex = (currentIndex + 1) % roles.length;
      setCurrentRole(roles[currentIndex]);
    }, 5000); // 每5秒切换一次
    
    return () => clearInterval(intervalId);
  }, []);
  
  const scrollToSection = (sectionId) => {
    const section = document.getElementById(sectionId);
    if (section) {
      window.scrollTo({
        top: section.offsetTop - 80,
        behavior: 'smooth'
      });
    }
  };
  
  return (
    <>
      <PageSection id="home">
        <HeroSection>
          <HeroTitle>
            <DecryptedText 
              text="你好，我是 Tully" 
              animateOn="view"
              speed={150}
              sequential={true}
              revealDirection="center"
              maxIterations={20}
              className=""
            />
          </HeroTitle>
          <Subtitle>
            <DecryptedText 
              text="后台进程：猫咪正在抢占 CPU（呼噜声 "
              animateOn="view"
              speed={120}
              sequential={true}
              revealDirection="start"
              maxIterations={15}
              className="highlighted"
              encryptedClassName="encrypted"
            />
            <CountUp
              from={0}
              to={99}
              duration={3}
              delay={2.5}
              className="highlighted"
            />
            <DecryptedText 
              text="%）"
              animateOn="view"
              speed={120}
              sequential={true}
              revealDirection="start"
              maxIterations={15}
              className="highlighted"
              encryptedClassName="encrypted"
            />
          </Subtitle>
          <ButtonContainer
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <PrimaryButton 
              href="#projects"
              whileHover={{ y: -5, boxShadow: "0 20px 30px rgba(0, 0, 0, 0.3)" }}
              whileTap={{ y: 0 }}
              onClick={(e) => {
                e.preventDefault();
                scrollToSection('projects');
              }}
            >
              查看我的项目
            </PrimaryButton>
            
            <SecondaryButton 
              href="#contact"
              whileHover={{ y: -5, borderColor: "#4cc9f0" }}
              whileTap={{ y: 0 }}
              onClick={(e) => {
                e.preventDefault();
                scrollToSection('contact');
              }}
            >
              联系我
            </SecondaryButton>
          </ButtonContainer>
        </HeroSection>
        
        <ScrollIndicator
          initial={{ opacity: 0 }}
          animate={{ opacity: 1, y: [0, 10, 0] }}
          transition={{ 
            opacity: { delay: 1, duration: 1 },
            y: { repeat: Infinity, duration: 1.5, ease: "easeInOut" }
          }}
          onClick={() => scrollToSection('projects')}
          style={{ cursor: 'pointer' }}
        >
          <span>向下滚动</span>
          <i className="fas fa-chevron-down"></i>
        </ScrollIndicator>
      </PageSection>
      
      <FeaturedProjects />
    </>
  );
};

export default Home; 