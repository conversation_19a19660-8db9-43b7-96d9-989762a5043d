import { useState, useEffect } from 'react';
import styled from 'styled-components';

const TypewriterContainer = styled.div`
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  min-height: 6rem;
`;

const Cursor = styled.span`
  display: inline-block;
  width: 3px;
  height: 1em;
  background-color: currentColor;
  margin-left: 2px;
  animation: blink 1s step-end infinite;
  
  @keyframes blink {
    from, to { opacity: 1; }
    50% { opacity: 0; }
  }
`;

export default function Typewriter({ phrases, typingSpeed = 100, pauseTime = 2000 }) {
  const [displayText, setDisplayText] = useState('');
  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);
  const [isTyping, setIsTyping] = useState(true);
  
  useEffect(() => {
    let timer;
    const currentPhrase = phrases[currentPhraseIndex];
    
    if (isTyping) {
      if (displayText.length < currentPhrase.length) {
        timer = setTimeout(() => {
          setDisplayText(currentPhrase.substring(0, displayText.length + 1));
        }, typingSpeed);
      } else {
        setIsTyping(false);
        timer = setTimeout(() => {
          setIsTyping(true);
          setDisplayText('');
          setCurrentPhraseIndex((currentPhraseIndex + 1) % phrases.length);
        }, pauseTime);
      }
    }
    
    return () => clearTimeout(timer);
  }, [displayText, currentPhraseIndex, isTyping, phrases, typingSpeed, pauseTime]);
  
  return (
    <TypewriterContainer>
      {displayText}
      <Cursor />
    </TypewriterContainer>
  );
} 