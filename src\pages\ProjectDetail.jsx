import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import projects from '../data/projects';

const DetailContainer = styled.div`
  min-height: 100vh;
  background: ${({ theme }) => theme.background};
  color: ${({ theme }) => theme.text};
  padding-top: 80px;
`;

const BackButton = styled.button`
  position: fixed;
  top: 100px;
  left: 20px;
  z-index: 100;
  background: ${({ theme }) => theme.primary};
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

  &:hover {
    background: ${({ theme }) => theme.primaryHover || theme.primary};
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }

  @media (max-width: 768px) {
    top: 80px;
    left: 15px;
    padding: 10px 16px;
    font-size: 0.8rem;
  }
`;

const HeroSection = styled.section`
  padding: 2rem 0 4rem;
  background: linear-gradient(135deg, #f0f5fb 0%, #e8f4f8 100%);
  
  @media (max-width: 768px) {
    padding: 1rem 0 2rem;
  }
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  
  @media (max-width: 768px) {
    padding: 0 1rem;
  }
`;

const Title = styled.h1`
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: bold;
  color: #333;
  margin-bottom: 1.5rem;
  text-align: center;
`;

const Subtitle = styled.p`
  font-size: clamp(1rem, 2vw, 1.25rem);
  color: #666;
  max-width: 800px;
  margin: 0 auto 2rem;
  text-align: center;
  line-height: 1.6;
`;

const ButtonGroup = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
`;

const ActionButton = styled.a`
  background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  }

  &.demo {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    
    &:hover {
      box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
    }
  }
`;

const OverviewSection = styled.section`
  padding: 4rem 0;
  background: white;
`;

const SectionTitle = styled.h2`
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  font-weight: bold;
  color: #333;
  margin-bottom: 2rem;
  text-align: center;
`;

const OverviewGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
`;

const OverviewCard = styled.div`
  background: #f8fafc;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-5px);
  }
`;

const CardIcon = styled.div`
  width: 56px;
  height: 56px;
  background: ${props => props.bg || '#3B82F6'};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.5rem;
`;

const CardTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.75rem;
`;

const CardDescription = styled.p`
  color: #666;
  line-height: 1.6;
`;

const FeaturesSection = styled.section`
  padding: 4rem 0;
  background: #f8fafc;
`;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const ContactSection = styled.section`
  padding: 4rem 0;
  background: white;
`;

const ContactForm = styled.form`
  max-width: 600px;
  margin: 0 auto;
  display: grid;
  gap: 1.5rem;
`;

const FormGroup = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const FormField = styled.div`
  display: flex;
  flex-direction: column;
`;

const Label = styled.label`
  font-size: 0.9rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
`;

const Input = styled.input`
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
`;

const TextArea = styled.textarea`
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  resize: vertical;
  min-height: 120px;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
`;

const SubmitButton = styled.button`
  background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  justify-self: center;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  }
`;

const FeatureCard = styled.div`
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }
`;

const FeatureImage = styled.img`
  width: 100%;
  height: 250px;
  object-fit: cover;
`;

const FeatureContent = styled.div`
  padding: 1.5rem;
`;

const FeatureTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.75rem;
`;

const FeatureDescription = styled.p`
  color: #666;
  line-height: 1.6;
  margin-bottom: 1rem;
`;

const TagContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
`;

const Tag = styled.span`
  background: ${props => props.bg || '#dbeafe'};
  color: ${props => props.color || '#3B82F6'};
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-weight: 500;
`;

const ProjectDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  const project = projects.find(p => p.id === parseInt(id));
  
  if (!project) {
    return (
      <DetailContainer>
        <Container>
          <Title>项目未找到</Title>
          <div style={{ textAlign: 'center' }}>
            <ActionButton onClick={() => navigate('/')}>
              ← 返回首页
            </ActionButton>
          </div>
        </Container>
      </DetailContainer>
    );
  }

  const handleDemoClick = (e) => {
    e.preventDefault();
    if (navigator.clipboard) {
      navigator.clipboard.writeText(project.demoInfo).then(() => {
        alert(`已复制到剪贴板：${project.demoInfo}`);
      }).catch(() => {
        alert(`体验方式：${project.demoInfo}`);
      });
    } else {
      alert(`体验方式：${project.demoInfo}`);
    }
  };

  return (
    <DetailContainer>
      <BackButton onClick={() => navigate('/')}>
        ← 返回首页
      </BackButton>

      <HeroSection>
        <Container>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Title>
              {project.id === 3 ? `👩🏻‍🎓${project.title}📚` : project.title}
            </Title>
            <Subtitle>
              {project.id === 3 
                ? "全方位支持学生作业提交、教师审核、积分管理的数字化学习平台\n让学习过程更高效、更透明、更有趣"
                : project.description
              }
            </Subtitle>
            
            <ButtonGroup>
              {project.liveUrl && (
                <ActionButton href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                  🌐 在线访问
                </ActionButton>
              )}
              {project.demoInfo && (
                <ActionButton className="demo" onClick={handleDemoClick}>
                  📱 获取体验方式
                </ActionButton>
              )}
            </ButtonGroup>
          </motion.div>
        </Container>
      </HeroSection>

      {project.id === 3 && (
        <OverviewSection>
          <Container>
            <SectionTitle>系统概览</SectionTitle>
            <p style={{ textAlign: 'center', color: '#666', marginBottom: '3rem', maxWidth: '800px', margin: '0 auto 3rem' }}>
              学生积分与作业管理系统是一款专为教育场景设计的综合性平台，支持多角色协同工作，提供作业提交、审核、积分管理等核心功能
            </p>
            
            <OverviewGrid>
              <OverviewCard>
                <CardIcon bg="#dbeafe">👥</CardIcon>
                <CardTitle>多角色支持</CardTitle>
                <CardDescription>
                  系统支持学生、教师、家长三种角色，每个角色拥有独立的功能模块和操作权限，满足不同用户的需求
                </CardDescription>
              </OverviewCard>
              
              <OverviewCard>
                <CardIcon bg="#dcfce7">📤</CardIcon>
                <CardTitle>便捷作业管理</CardTitle>
                <CardDescription>
                  学生可以轻松上传各类作业、试卷和视频，教师可以快速审核并给予反馈，整个过程高效透明
                </CardDescription>
              </OverviewCard>
              
              <OverviewCard>
                <CardIcon bg="#fef3c7">🏆</CardIcon>
                <CardTitle>积分激励系统</CardTitle>
                <CardDescription>
                  通过积分机制激励学生积极参与学习活动，家长和教师可以随时查看积分统计，了解学生的学习积极性
                </CardDescription>
              </OverviewCard>
            </OverviewGrid>
          </Container>
        </OverviewSection>
      )}

      <FeaturesSection>
        <Container>
          <SectionTitle>功能界面展示</SectionTitle>
          <p style={{ textAlign: 'center', color: '#666', marginBottom: '3rem', maxWidth: '800px', margin: '0 auto 3rem' }}>
            探索系统的各个功能模块，了解如何通过我们的平台提升学习效率和管理体验
          </p>
          
          <FeaturesGrid>
            {project.id === 3 ? (
              // 学生积分管理小程序的特定功能展示
              [
                { image: '/images/projects/stu_logo0.png', title: '系统登录', desc: '支持学生、教师、家长三种角色登录，提供简洁直观的登录界面，确保用户快速进入系统', tags: [{ text: '多角色认证', bg: '#dbeafe', color: '#3B82F6' }, { text: '快速登录', bg: '#dcfce7', color: '#10B981' }, { text: '安全可靠', bg: '#e9d5ff', color: '#8B5CF6' }] },
                { image: '/images/projects/stu_logo1.png', title: '家长仪表板', desc: '家长可以查看学生数量、待审核任务、积分统计等关键指标，全面了解孩子的学习情况', tags: [{ text: '数据概览', bg: '#dbeafe', color: '#3B82F6' }, { text: '实时更新', bg: '#dcfce7', color: '#10B981' }, { text: '学习追踪', bg: '#e9d5ff', color: '#8B5CF6' }] },
                { image: '/images/projects/stu0.jpg', title: '学生中心', desc: '学生的个人中心，展示个人信息、积分情况、待完成任务等，是学生管理学习活动的核心入口', tags: [{ text: '个人信息', bg: '#dbeafe', color: '#3B82F6' }, { text: '任务管理', bg: '#dcfce7', color: '#10B981' }, { text: '学习计划', bg: '#e9d5ff', color: '#8B5CF6' }] },
                { image: '/images/projects/stu1.png', title: '作业上传', desc: '学生可以轻松上传各类作业、试卷和视频，系统支持多文件上传、格式验证和进度提示', tags: [{ text: '多格式支持', bg: '#dbeafe', color: '#3B82F6' }, { text: '拖放上传', bg: '#dcfce7', color: '#10B981' }, { text: '进度反馈', bg: '#e9d5ff', color: '#8B5CF6' }] },
                { image: '/images/projects/stu4.png', title: '作业审核', desc: '教师可以对待提交的作业和试卷进行审核，提供评分和反馈，支持通过或打回操作', tags: [{ text: '作业管理', bg: '#dbeafe', color: '#3B82F6' }, { text: '评分系统', bg: '#dcfce7', color: '#10B981' }, { text: '反馈机制', bg: '#e9d5ff', color: '#8B5CF6' }] },
                { image: '/images/projects/stu2.png', title: '历史记录', desc: '查看学生的历史作业提交记录，包括提交时间、审核状态、积分变动等详细信息，方便回顾学习历程', tags: [{ text: '学习档案', bg: '#dbeafe', color: '#3B82F6' }, { text: '时间轴', bg: '#dcfce7', color: '#10B981' }, { text: '筛选查询', bg: '#e9d5ff', color: '#8B5CF6' }] },
                { image: '/images/projects/stu6.jpg', title: '积分管理', desc: '教师可以根据学生的表现进行积分奖励或扣除，支持批量操作和详细记录，确保积分公平透明', tags: [{ text: '积分调整', bg: '#dbeafe', color: '#3B82F6' }, { text: '记录追踪', bg: '#dcfce7', color: '#10B981' }, { text: '批量处理', bg: '#e9d5ff', color: '#8B5CF6' }] }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <FeatureCard>
                    <FeatureImage src={feature.image} alt={feature.title} />
                    <FeatureContent>
                      <FeatureTitle>{feature.title}</FeatureTitle>
                      <FeatureDescription>{feature.desc}</FeatureDescription>
                      <TagContainer>
                        {feature.tags.map((tag, tagIndex) => (
                          <Tag key={tagIndex} bg={tag.bg} color={tag.color}>
                            {tag.text}
                          </Tag>
                        ))}
                      </TagContainer>
                    </FeatureContent>
                  </FeatureCard>
                </motion.div>
              ))
            ) : (
              // 其他项目的通用展示
              project.galleryImages && project.galleryImages.map((image, index) => {
                const feature = { 
                  title: `功能展示 ${index + 1}`, 
                  desc: '项目功能展示', 
                  tags: [{ text: '功能特性', bg: '#dbeafe', color: '#3B82F6' }] 
                };
                
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                  >
                    <FeatureCard>
                      <FeatureImage src={image} alt={feature.title} />
                      <FeatureContent>
                        <FeatureTitle>{feature.title}</FeatureTitle>
                        <FeatureDescription>{feature.desc}</FeatureDescription>
                        <TagContainer>
                          {feature.tags.map((tag, tagIndex) => (
                            <Tag key={tagIndex} bg={tag.bg} color={tag.color}>
                              {tag.text}
                            </Tag>
                          ))}
                        </TagContainer>
                      </FeatureContent>
                    </FeatureCard>
                  </motion.div>
                );
              })
            )}
          </FeaturesGrid>
        </Container>
      </FeaturesSection>

      {project.id === 3 && (
        <ContactSection>
          <Container>
            <SectionTitle>联系我们</SectionTitle>
            <p style={{ textAlign: 'center', color: '#666', marginBottom: '3rem', maxWidth: '600px', margin: '0 auto 3rem' }}>
              对系统有任何疑问或建议？请填写下方表单与我们联系
            </p>
            
            <ContactForm onSubmit={(e) => e.preventDefault()}>
              <FormGroup>
                <FormField>
                  <Label htmlFor="name">姓名</Label>
                  <Input type="text" id="name" name="name" placeholder="请输入您的姓名" />
                </FormField>
                <FormField>
                  <Label htmlFor="email">邮箱</Label>
                  <Input type="email" id="email" name="email" placeholder="请输入您的邮箱" />
                </FormField>
              </FormGroup>
              
              <FormField>
                <Label htmlFor="subject">主题</Label>
                <Input type="text" id="subject" name="subject" placeholder="请输入咨询主题" />
              </FormField>
              
              <FormField>
                <Label htmlFor="message">留言内容</Label>
                <TextArea id="message" name="message" placeholder="请输入您的留言内容" />
              </FormField>
              
              <SubmitButton type="submit">
                📧 发送留言
              </SubmitButton>
            </ContactForm>
          </Container>
        </ContactSection>
      )}
    </DetailContainer>
  );
};

export default ProjectDetail;